CREATE TABLE "charsheet_account" (
	"userId" varchar(255) NOT NULL,
	"type" varchar(255) NOT NULL,
	"provider" varchar(255) NOT NULL,
	"providerAccountId" varchar(255) NOT NULL,
	"refresh_token" text,
	"access_token" text,
	"expires_at" integer,
	"token_type" varchar(255),
	"scope" varchar(255),
	"id_token" text,
	"session_state" varchar(255),
	CONSTRAINT "charsheet_account_provider_providerAccountId_pk" PRIMARY KEY("provider","providerAccountId")
);
--> statement-breakpoint
CREATE TABLE "charsheet_character" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "charsheet_character_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"content" text NOT NULL,
	"createdById" varchar(255) NOT NULL,
	"createdAt" timestamp with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "charsheet_dice_roll" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "charsheet_dice_roll_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"playerId" varchar(255) NOT NULL,
	"characterId" integer NOT NULL,
	"date" timestamp with time zone DEFAULT now() NOT NULL,
	"attribute" varchar(50),
	"attributeValue" integer,
	"skill" varchar(50),
	"skillValue" integer,
	"additionalDice" integer NOT NULL,
	"result" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "charsheet_session" (
	"sessionToken" varchar(255) PRIMARY KEY NOT NULL,
	"userId" varchar(255) NOT NULL,
	"expires" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE "charsheet_user" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"name" varchar(255),
	"email" varchar(255) NOT NULL,
	"emailVerified" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
	"image" varchar(255),
	"isAdmin" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "charsheet_verification_token" (
	"identifier" varchar(255) NOT NULL,
	"token" varchar(255) NOT NULL,
	"expires" timestamp with time zone NOT NULL,
	CONSTRAINT "charsheet_verification_token_identifier_token_pk" PRIMARY KEY("identifier","token")
);
--> statement-breakpoint
ALTER TABLE "charsheet_account" ADD CONSTRAINT "charsheet_account_userId_charsheet_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."charsheet_user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "charsheet_character" ADD CONSTRAINT "charsheet_character_createdById_charsheet_user_id_fk" FOREIGN KEY ("createdById") REFERENCES "public"."charsheet_user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "charsheet_dice_roll" ADD CONSTRAINT "charsheet_dice_roll_playerId_charsheet_user_id_fk" FOREIGN KEY ("playerId") REFERENCES "public"."charsheet_user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "charsheet_dice_roll" ADD CONSTRAINT "charsheet_dice_roll_characterId_charsheet_character_id_fk" FOREIGN KEY ("characterId") REFERENCES "public"."charsheet_character"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "charsheet_session" ADD CONSTRAINT "charsheet_session_userId_charsheet_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."charsheet_user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "account_user_id_idx" ON "charsheet_account" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "created_by_idx" ON "charsheet_character" USING btree ("createdById");--> statement-breakpoint
CREATE INDEX "dice_roll_player_idx" ON "charsheet_dice_roll" USING btree ("playerId");--> statement-breakpoint
CREATE INDEX "dice_roll_character_idx" ON "charsheet_dice_roll" USING btree ("characterId");--> statement-breakpoint
CREATE INDEX "t_user_id_idx" ON "charsheet_session" USING btree ("userId");