{"id": "aa698468-ebac-4804-a2e4-5f09f74a1283", "prevId": "8e234427-a09c-4fba-a618-9955919cc642", "version": "7", "dialect": "postgresql", "tables": {"public.charsheet_account": {"name": "charsheet_account", "schema": "", "columns": {"userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {"account_user_id_idx": {"name": "account_user_id_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"charsheet_account_userId_charsheet_user_id_fk": {"name": "charsheet_account_userId_charsheet_user_id_fk", "tableFrom": "charsheet_account", "tableTo": "charsheet_user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"charsheet_account_provider_providerAccountId_pk": {"name": "charsheet_account_provider_providerAccountId_pk", "columns": ["provider", "providerAccountId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.charsheet_character": {"name": "charsheet_character", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "charsheet_character_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "**********", "cache": "1", "cycle": false}}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdById": {"name": "createdById", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"created_by_idx": {"name": "created_by_idx", "columns": [{"expression": "createdById", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"charsheet_character_createdById_charsheet_user_id_fk": {"name": "charsheet_character_createdById_charsheet_user_id_fk", "tableFrom": "charsheet_character", "tableTo": "charsheet_user", "columnsFrom": ["createdById"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.charsheet_dice_roll": {"name": "charsheet_dice_roll", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "charsheet_dice_roll_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "**********", "cache": "1", "cycle": false}}, "playerId": {"name": "playerId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "characterId": {"name": "characterId", "type": "integer", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "attribute": {"name": "attribute", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "attributeValue": {"name": "attributeValue", "type": "integer", "primaryKey": false, "notNull": false}, "skill": {"name": "skill", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "skillValue": {"name": "skillValue", "type": "integer", "primaryKey": false, "notNull": false}, "additionalDice": {"name": "additionalDice", "type": "integer", "primaryKey": false, "notNull": true}, "isWillpowerReroll": {"name": "isWillpowerReroll", "type": "boolean", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"dice_roll_player_idx": {"name": "dice_roll_player_idx", "columns": [{"expression": "playerId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dice_roll_character_idx": {"name": "dice_roll_character_idx", "columns": [{"expression": "characterId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"charsheet_dice_roll_playerId_charsheet_user_id_fk": {"name": "charsheet_dice_roll_playerId_charsheet_user_id_fk", "tableFrom": "charsheet_dice_roll", "tableTo": "charsheet_user", "columnsFrom": ["playerId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "charsheet_dice_roll_characterId_charsheet_character_id_fk": {"name": "charsheet_dice_roll_characterId_charsheet_character_id_fk", "tableFrom": "charsheet_dice_roll", "tableTo": "charsheet_character", "columnsFrom": ["characterId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.charsheet_session": {"name": "charsheet_session", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {"t_user_id_idx": {"name": "t_user_id_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"charsheet_session_userId_charsheet_user_id_fk": {"name": "charsheet_session_userId_charsheet_user_id_fk", "tableFrom": "charsheet_session", "tableTo": "charsheet_user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.charsheet_user": {"name": "charsheet_user", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "isAdmin": {"name": "isAdmin", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.charsheet_verification_token": {"name": "charsheet_verification_token", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"charsheet_verification_token_identifier_token_pk": {"name": "charsheet_verification_token_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}