CREATE TABLE "charsheet_character_share" (
	"characterId" integer NOT NULL,
	"sharedWithId" varchar(255) NOT NULL,
	"createdAt" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "charsheet_character_share_characterId_sharedWithId_pk" PRIMARY KEY("characterId","sharedWithId")
);
--> statement-breakpoint
CREATE TABLE "lore_items" (
	"id" serial PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"created_by_id" text
);
--> statement-breakpoint
ALTER TABLE "charsheet_dice_roll" DROP CONSTRAINT "charsheet_dice_roll_originalRollId_charsheet_dice_roll_id_fk";
--> statement-breakpoint
ALTER TABLE "charsheet_dice_roll" ADD COLUMN "specialization" varchar(50);--> statement-breakpoint
ALTER TABLE "charsheet_character_share" ADD CONSTRAINT "charsheet_character_share_characterId_charsheet_character_id_fk" FOREIGN KEY ("characterId") REFERENCES "public"."charsheet_character"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "charsheet_character_share" ADD CONSTRAINT "charsheet_character_share_sharedWithId_charsheet_user_id_fk" FOREIGN KEY ("sharedWithId") REFERENCES "public"."charsheet_user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lore_items" ADD CONSTRAINT "lore_items_created_by_id_charsheet_user_id_fk" FOREIGN KEY ("created_by_id") REFERENCES "public"."charsheet_user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "charsheet_dice_roll" ADD CONSTRAINT "dice_roll_original_roll_id_fkey" FOREIGN KEY ("originalRollId") REFERENCES "public"."charsheet_dice_roll"("id") ON DELETE no action ON UPDATE no action;