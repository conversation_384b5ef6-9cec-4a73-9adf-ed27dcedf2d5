- change character type to this:
{
  id: number;
  baseData: {
    name: string;
    concept: string;
    icon: string;
    additionalInformation: { name: string, value: string }[]
  }
  createdAt: Date;
  updatedAt: Date;

  attributes: { name: string, value: number }[]
  skills: { name: string, value: number, zeroValuePenalty: number }[]

  trackers: {
    reputation: number;
    health: TrackerState;
    willpower: TrackerState;
  }
}

- sqlify
- confirm on remove item / merit