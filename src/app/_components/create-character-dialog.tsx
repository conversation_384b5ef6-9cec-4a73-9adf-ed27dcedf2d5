'use client';

import { type ReactNode } from 'react';
import { useState } from 'react';
import { Button } from '~/app/_components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '~/app/_components/ui/dialog';
import { api } from '../../trpc/react';
import {
    initializeAttributes,
    initializeMerits,
    initializeSkills,
    SKILLS,
    type Character
} from './character-sheet/types';
import { useRouter } from 'next/navigation';
import { Textarea } from './ui/textarea';
import { useSession } from 'next-auth/react';

const defaultCharacter: Omit<Character, 'id'> = {
    name: '',
    concept: '',
    virtue: '',
    vice: '',
    icon: '',
    health: { filled: 1, damaged: 0 },
    willpower: { filled: 1, damaged: 0 },
    attributes: initializeAttributes(),
    skills: initializeSkills(),
    merits: initializeMerits(),
    inventory: [],
    ambitions: ['', '', ''],
    skillSpecializations: SKILLS.reduce((acc, skillGroup) => {
        skillGroup.forEach((skill) => {
            acc[skill] = [];
        });
        return acc;
    }, {} as Record<string, string[]>)
};

interface CreateCharacterDialogProps {
    children?: ReactNode;
}

export function CreateCharacterDialog({
    children
}: CreateCharacterDialogProps) {
    const router = useRouter();
    const createMutation = api.character.create.useMutation({
        onSuccess: (newCharacter) => {
            if (newCharacter) {
                router.refresh();
                router.push(`/characters/${newCharacter.id}`);
            }
        }
    });
    const generateMutation = api.character.generateFromDescription.useMutation({
        onSuccess: (newCharacter) => {
            if (newCharacter) {
                router.refresh();
                router.push(`/characters/${newCharacter.id}?waitForImage=true`);
            }
        }
    });
    const [description, setDescription] = useState('');
    const { data: session } = useSession();

    const onCreateNewCharacter = async () => {
        await createMutation.mutateAsync({
            content: JSON.stringify(defaultCharacter)
        });
    };

    const dialogContent = (
        <DialogContent className="sm:max-w-md">
            <DialogHeader>
                <DialogTitle>Create or Import Character</DialogTitle>
                <div className="py-4 space-y-4">
                    <div className="flex flex-col gap-2">
                        <Button
                            onClick={onCreateNewCharacter}
                            disabled={createMutation.isPending}
                            variant="outline"
                            className="w-full"
                        >
                            {createMutation.isPending
                                ? 'Creating...'
                                : 'Create New Character'}
                        </Button>
                        {/* Existing import button */}
                        <label>
                            <Button
                                variant="outline"
                                className="w-full"
                                asChild
                            >
                                <span>Import Character</span>
                            </Button>
                            <input
                                type="file"
                                accept=".codjson,application/json"
                                className="hidden"
                                onChange={async (e) => {
                                    const file = e.target.files?.[0];
                                    if (!file) return;

                                    const reader = new FileReader();
                                    reader.onload = async (event) => {
                                        try {
                                            const content = event.target
                                                ?.result as string;
                                            const imported =
                                                JSON.parse(content);
                                            const newCharacter =
                                                await createMutation.mutateAsync(
                                                    {
                                                        content:
                                                            JSON.stringify(
                                                                imported
                                                            )
                                                    }
                                                );
                                            if (newCharacter) {
                                                const shouldWaitForImage =
                                                    Boolean(
                                                        imported.gmNotes
                                                            ?.imageGenerationPrompt
                                                    )

                                                router.refresh()
                                                router.push(
                                                    shouldWaitForImage
                                                        ? `/characters/${newCharacter.id}?waitForImage=true`
                                                        : `/characters/${newCharacter.id}`
                                                )
                                            }
                                        } catch (err) {
                                            console.error(
                                                'Invalid character file'
                                            );
                                        }
                                    };
                                    reader.readAsText(file);
                                }}
                            />
                        </label>

                        {/* Add AI Generation section */}
                        {session?.user?.isAdmin && (
                            <div className="space-y-2">
                                <Textarea
                                    placeholder="Enter character description..."
                                    value={description}
                                    onChange={(val) => setDescription(val)}
                                />
                                <Button
                                    onClick={() =>
                                        generateMutation.mutate({ description })
                                    }
                                    disabled={
                                        generateMutation.isPending ||
                                        !description
                                    }
                                    variant="outline"
                                    className="w-full"
                                >
                                    {generateMutation.isPending
                                        ? 'Generating...'
                                        : 'Generate with AI'}
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </DialogHeader>
        </DialogContent>
    );

    return (
        <Dialog>
            <DialogTrigger asChild>
                {children || <Button>Create Character</Button>}
            </DialogTrigger>
            {dialogContent}
        </Dialog>
    );
}
