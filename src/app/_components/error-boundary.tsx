'use client';

import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardFooter } from './ui/card';
import { useRouter } from 'next/navigation';

interface ErrorBoundaryProps {
    error: Error;
    reset: () => void;
}

export function ErrorBoundary({ error, reset }: ErrorBoundaryProps) {
    const router = useRouter();

    return (
        <div className="container flex items-center justify-center min-h-screen">
            <Card className="w-[420px]">
                <CardContent className="pt-6">
                    <div className="flex flex-col gap-4">
                        <h2 className="text-lg font-semibold">Something went wrong!</h2>
                        <p className="text-sm text-muted-foreground">{error.message}</p>
                    </div>
                </CardContent>
                <CardFooter className="flex gap-2">
                    <Button onClick={() => router.push('/characters')} variant="outline">
                        Back to Characters
                    </Button>
                    <Button onClick={reset}>Try Again</Button>
                </CardFooter>
            </Card>
        </div>
    );
}