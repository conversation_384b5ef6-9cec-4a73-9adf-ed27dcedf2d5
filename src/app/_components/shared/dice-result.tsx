'use client';

interface DiceResultProps {
    result?: number;
    isSuccess?: boolean;
    size?: 'sm' | 'md' | 'lg';
    isLoading?: boolean;
}

const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
};

export function DiceResult({
    result,
    isSuccess,
    size = 'md',
    isLoading = false
}: DiceResultProps) {
    return (
        <div
            className={`flex items-center justify-center rounded-md border ${
                isLoading
                    ? 'border-muted animate-spin'
                    : isSuccess
                    ? 'border-primary font-bold'
                    : 'border-muted'
            } ${sizeClasses[size]} ${result === 10 ? 'bg-primary/10' : ''}`}
            style={
                isLoading
                    ? { animationDelay: `${Math.random() * -1}s` }
                    : undefined
            }
        >
            {isLoading ? '?' : result}
        </div>
    );
}

interface DiceResultsProps {
    results: number[];
    isChanceDie: boolean;
    size?: 'sm' | 'md' | 'lg';
    isLoading?: boolean;
    diceCount?: number;
    isWillpowerReroll?: boolean;
}

export function DiceResults({
    results,
    isChanceDie,
    size = 'md',
    isLoading = false,
    diceCount = 0,
    isWillpowerReroll
}: DiceResultsProps) {
    if (isLoading) {
        return (
            <div className="flex flex-wrap gap-2">
                {Array.from({
                    length: diceCount
                }).map((_, index) => (
                    <DiceResult
                        key={`loading-${index}`}
                        result={isWillpowerReroll ? results[index] : undefined}
                        isLoading={
                            isWillpowerReroll
                                ? results[index] === undefined
                                : true
                        }
                        size={size}
                    />
                ))}
            </div>
        )
    }

    return (
        <div className="flex flex-wrap gap-2">
            {results.map((result, index) => (
                <DiceResult
                    key={`${result}-${index}`}
                    result={result}
                    isSuccess={
                        (isChanceDie && result === 10) ||
                        (!isChanceDie && result >= 8)
                    }
                    size={size}
                />
            ))}
        </div>
    );
}
