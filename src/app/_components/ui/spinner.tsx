import { cn } from "~/_lib/utils";

interface SpinnerProps {
    className?: string;
    size?: 'sm' | 'default' | 'lg';
}

export function Spinner({ className, size = 'default' }: SpinnerProps) {
    return (
        <div
            className={cn(
                "animate-spin rounded-full border-2 border-current border-t-transparent",
                size === 'sm' && "h-4 w-4",
                size === 'default' && "h-8 w-8",
                size === 'lg' && "h-12 w-12",
                className
            )}
        />
    );
}