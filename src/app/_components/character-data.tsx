"use client";

import CharacterSheet from './character-sheet';
import { Header } from './layout/header';
import { ExportButton } from './character-sheet/export-button';
import type { Character } from './character-sheet/types';
import { useCallback, useEffect, useState, useRef } from 'react';
import { api } from '../../trpc/react';
import { useUserNames } from '../_contexts/user-names-context';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { ShareCharacterDialog } from './character-sheet/share-character-dialog';
import { useIsAdmin } from '../../lib/auth';

const SAVE_DELAY = 500;
const IMAGE_POLL_INTERVAL = 15000;

export function CharacterData({
    savedCharacter,
    createdById
}: {
    savedCharacter: Character;
    createdById: string;
}) {
    const [character, setCharacter] = useState<Character>(saved<PERSON>haracter);
    const isDirtyRef = useRef(false);
    const userNames = useUserNames();
    const session = useSession();
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();
    const getCharacterQuery = api.character.getById.useQuery(
        { id: savedCharacter.id },
        { enabled: false }
    );

    const updateMutation = api.character.update.useMutation();

    const debouncedSave = useCallback(
        async (updatedCharacter: Character) => {
            try {
                await updateMutation.mutateAsync({
                    id: savedCharacter.id,
                    content: JSON.stringify(updatedCharacter)
                });
                isDirtyRef.current = false;
            } catch (error) {
                console.error('Failed to save character:', error);
            }
        },
        [updateMutation.mutateAsync, savedCharacter.id]
    );

    useEffect(() => {
        if (!isDirtyRef.current) return;

        const timeoutId = setTimeout(() => {
            debouncedSave(character);
        }, SAVE_DELAY);

        return () => clearTimeout(timeoutId);
    }, [character, debouncedSave]);

    const handleCharacterChange = useCallback(
        (updater: (prev: Character) => Character) => {
            setCharacter(updater);
            isDirtyRef.current = true;
        },
        []
    );

    // Poll for image if waitForImage query param is present
    useEffect(() => {
        if (!searchParams.get('waitForImage')) return;
        const pollInterval = setInterval(async () => {
            try {
                const updatedCharacter = await getCharacterQuery.refetch();
                if (
                    updatedCharacter.data?.imageUrl &&
                    updatedCharacter.data?.imageUrl !== character.imageUrl
                ) {
                    setCharacter((prev) => ({
                        ...prev,
                        imageUrl: updatedCharacter.data?.imageUrl ?? undefined
                    }));
                    clearInterval(pollInterval);
                    // remove search param using Next.js router
                    const params = new URLSearchParams(searchParams);
                    params.delete('waitForImage');
                    router.replace(`${pathname}?${params.toString()}`);
                }
            } catch (error) {
                console.error('Failed to poll for character image:', error);
                clearInterval(pollInterval);
                // remove search param using Next.js router
                const params = new URLSearchParams(searchParams);
                params.delete('waitForImage');
                router.replace(`${pathname}?${params.toString()}`);
            }
        }, IMAGE_POLL_INTERVAL);

        return () => clearInterval(pollInterval);
    }, [searchParams, character.imageUrl, getCharacterQuery, router, pathname]);

    const isAdmin = useIsAdmin();
    return (
        <div className="min-h-screen flex flex-col">
            <Header backHref="/characters" backText="Back to Characters">
                <div className="flex gap-2">
                    {isAdmin && (
                        <ShareCharacterDialog
                            characterId={savedCharacter.id}
                            originalConcept={character.concept}
                        />
                    )}
                    <ExportButton character={character} />
                </div>
            </Header>
            <main className="flex-1 relative">
                <CharacterSheet
                    character={character}
                    setCharacter={handleCharacterChange}
                    creatorName={
                        userNames[createdById] ??
                        session.data?.user.name ??
                        'Unknown'
                    }
                />
            </main>
        </div>
    )
}
