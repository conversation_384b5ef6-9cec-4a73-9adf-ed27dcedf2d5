'use client';

import { X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from './ui/button';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from './ui/tooltip';
import { api } from '~/trpc/react';

interface DeleteCharacterButtonProps {
    id: number;
}

export function DeleteCharacterButton({ id }: DeleteCharacterButtonProps) {
    const router = useRouter();
    const deleteMutation = api.character.delete.useMutation({
        onSuccess: () => {
            router.refresh();
        }
    });

    const handleDelete = async (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        if (confirm('Are you sure you want to delete this character?')) {
            await deleteMutation.mutateAsync({ id });
        }
    };

    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="link"
                        size="icon"
                        className="absolute top-0 right-0 opacity-0 group-hover:opacity-200 transition-opacity text-white cursor-pointer"
                        onClick={handleDelete}
                        disabled={deleteMutation.isPending}
                    >
                        <X className="h-4 w-4" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent>
                    <p>Delete Character</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}
