'use client';

import Link from 'next/link';
import { Button } from '../ui/button';
import { useIsAdmin } from '~/lib/auth';
import { useState } from 'react';
import { RollHistoryModal } from '../roll-history-modal';
import { HistoryIcon } from 'lucide-react';
import { CharacterSearch } from '../character-search';
import { useSession } from 'next-auth/react';
import { GroupRollModal } from '../group-roll-modal'

interface HeaderProps {
    backHref?: string
    backText?: string
    children?: React.ReactNode
}

export function Header({ backHref, backText, children }: HeaderProps) {
    const isAdmin = useIsAdmin()
    const session = useSession()

    return (
        <header className="px-2 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-14 items-center justify-between">
                <div className="flex items-center gap-2">
                    {backHref && (
                        <Link href={backHref}>
                            <Button variant="ghost" className="gap-2">
                                ← {backText}
                            </Button>
                        </Link>
                    )}
                    {session.data?.user && <CharacterSearch />}
                    {isAdmin && <RollHistoryModal />}
                    {isAdmin && <GroupRollModal />}
                </div>
                <div className="flex flex-1 justify-end gap-2">{children}</div>
            </div>
        </header>
    )
}
