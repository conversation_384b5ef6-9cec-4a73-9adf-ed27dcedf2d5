import { DerivedStat } from './derived-stat';

interface DerivedStatsProps {
    defense: number;
    initiative: number;
    speed: number;
}

export function DerivedStatsSection({
    defense,
    initiative,
    speed
}: DerivedStatsProps) {
    return (
        <>
            <h3 className="font-bold">Derived Stats & Trackers</h3>
            <div className="grid gap-6 md:grid-cols-3">
                <DerivedStat
                    label="Defense"
                    value={defense}
                    tooltip={{
                        description:
                            'Melee attacks lose [defense] dice against you',
                        formula: 'Lower of (Dexterity, Wits) + Athletics'
                    }}
                />

                <DerivedStat
                    label="Initiative"
                    value={initiative}
                    tooltip={{
                        description:
                            'Order of actions in combat is d10 + [initiative]',
                        formula: 'Dexterity + Composure'
                    }}
                />

                <DerivedStat
                    label="Speed"
                    value={speed}
                    tooltip={{
                        description: 'You can move [speed] meters per turn',
                        formula: 'Strength + Dexterity + 5'
                    }}
                />
            </div>
        </>
    )
}
