'use client';

import { Download } from 'lucide-react';
import { Button } from '../ui/button';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '../ui/tooltip';
import type { Character } from './types';

interface ExportButtonProps {
    character: Character | undefined;
}

export function ExportButton({ character }: ExportButtonProps) {
    const handleExport = () => {
        const characterData = {
            ...character,
            // Exclude internal fields
            id: undefined,
            createdAt: undefined,
            updatedAt: undefined
        };

        const blob = new Blob([JSON.stringify(characterData, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${character?.name || 'character'}.codjson`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        disabled={!character}
                        variant="ghost"
                        size="icon"
                        onClick={handleExport}
                        className="h-9 w-9"
                    >
                        <Download className="h-4 w-4" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent>
                    <p>Export Character</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}