"use client";

import type { JSX } from 'react';
import { DotSelector } from './dot-selector';
import type { TrackerState } from './types';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '../ui/tooltip';

interface StatTrackerProps<T extends number | TrackerState> {
    label: string;
    customLabel?: JSX.Element;
    value: T;
    maxValue: number;
    totalDots?: number;
    allowDamage?: boolean;
    dangerThreshold?: number;
    onChange: (value: T) => void;
    tooltip?: {
        description: string;
        formula: string;
    };
}

export function StatTracker<T extends number | TrackerState>({
    label,
    value,
    maxValue,
    totalDots = 10,
    allowDamage = false,
    dangerThreshold,
    customLabel,
    tooltip,
    onChange
}: StatTrackerProps<T>) {
    const id = label.toLowerCase().replace(/\s+/g, '-');

    // Calculate current value for ARIA attributes
    const currentValue =
        typeof value === 'number' ? value : value.filled + value.damaged;

    const labelElement = customLabel ?? label;

    return (
        <div className="flex items-center justify-between">
            {tooltip ? (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <label
                                id={`${id}-label`}
                                htmlFor={id}
                                className="cursor-help"
                            >
                                {labelElement}
                            </label>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>{tooltip.description}</p>
                            <p>{tooltip.formula}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            ) : (
                <label id={`${id}-label`} htmlFor={id} className="font-medium">
                    {labelElement}
                </label>
            )}
            <div
                id={id}
                aria-labelledby={`${id}-label`}
                aria-valuemin={0}
                aria-valuemax={maxValue}
                aria-valuenow={currentValue}
            >
                <DotSelector<T>
                    value={value}
                    max={maxValue}
                    dangerThreshold={dangerThreshold}
                    totalDots={totalDots}
                    allowDamage={allowDamage}
                    onChange={onChange}
                />
            </div>
        </div>
    );
}
