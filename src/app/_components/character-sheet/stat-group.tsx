"use client";

import { DotSelector } from "./dot-selector";
import { SkillSpecializationDialog } from './skill-specialization-dialog';
import { MENTAL_SKILLS, type Skill } from './types';
import { useState } from 'react';
import {
    <PERSON>lt<PERSON>,
    TooltipContent,
    <PERSON><PERSON><PERSON>Provider,
    TooltipTrigger
} from '../ui/tooltip';

interface StatGroupProps<T extends string> {
    title: string;
    stats: T[][];
    values: Record<T, number>;
    disabled?: boolean;
    maxValue?: number;
    totalDots?: number;
    onChange: (stat: T, value: number) => void;
    selectedStat: T | null;
    onSelect: (stat: T) => void;
    specializations?: Partial<Record<T, string[]>>;
    onSpecializationChange?: (stat: T, specializations: string[]) => void;
}

export function formatSkillName(skill: Skill, value: number) {
    if (value === 0) {
        return (
            <span>
                {skill}
                <sup>-{MENTAL_SKILLS.some((s) => s === skill) ? 3 : 1}</sup>
            </span>
        );
    }
    return skill;
}

export function StatGroup<T extends string>({
    title,
    stats,
    values,
    maxValue = 5,
    totalDots = 5,
    disabled,
    onChange,
    selectedStat,
    onSelect,
    specializations,
    onSpecializationChange
}: StatGroupProps<T>) {
    const [specializationDialogOpen, setSpecializationDialogOpen] =
        useState(false);
    const [selectedStatForSpec, setSelectedStatForSpec] = useState<T | null>(
        null
    );

    const handleStatClick = (stat: T) => {
        if (onSpecializationChange) {
            setSelectedStatForSpec(stat);
            setSpecializationDialogOpen(true);
        } else if (disabled) {
            // disabled means it's locked
            onSelect(stat);
        }
    };

    const renderStatButton = (stat: T, displayName: React.ReactNode) => {
        const specs = specializations?.[stat];
        const hasSpecs = specs && specs.filter(Boolean).length > 0

        const button = (
            <button
                type="button"
                className={`${
                    !onSpecializationChange && !disabled ? '' : 'cursor-pointer'
                } outline-offset-2 text-left ${
                    selectedStat === stat ? 'font-bold text-primary' : ''
                }`}
                onClick={() => handleStatClick(stat)}
            >
                {displayName}
            </button>
        )

        if (hasSpecs) {
            return (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>{button}</TooltipTrigger>
                        <TooltipContent>
                            <p>
                                Specializations:{' '}
                                {specs.filter(Boolean).join(', ')}
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            )
        }

        return button;
    };

    return (
        <>
            <h3 className="font-bold">{title}</h3>
            <div className="grid gap-6 md:grid-cols-3">
                {stats.map((column) => (
                    <div
                        key={`${title}-column-${column.at(0)}`}
                        className="space-y-2"
                    >
                        {column.map((stat) => {
                            const displayName = formatSkillName(
                                stat as Skill,
                                values[stat]
                            );
                            return (
                                <div
                                    key={stat}
                                    className="flex items-center justify-between"
                                >
                                    {renderStatButton(stat, displayName)}
                                    <DotSelector
                                        disabled={disabled}
                                        value={values[stat]}
                                        max={maxValue}
                                        totalDots={totalDots}
                                        onChange={(value) =>
                                            onChange(stat, value)
                                        }
                                    />
                                </div>
                            );
                        })}
                    </div>
                ))}
            </div>
            {onSpecializationChange && selectedStatForSpec && (
                <SkillSpecializationDialog
                    skill={selectedStatForSpec as Skill}
                    specializations={
                        specializations?.[selectedStatForSpec] ?? []
                    }
                    isOpen={specializationDialogOpen}
                    onClose={() => {
                        setSpecializationDialogOpen(false);
                        setSelectedStatForSpec(null);
                    }}
                    onChange={(newSpecs) => {
                        onSpecializationChange(selectedStatForSpec, newSpecs);
                    }}
                />
            )}
        </>
    );
}
