import {
    Toolt<PERSON>,
    <PERSON><PERSON><PERSON>Content,
    <PERSON><PERSON><PERSON>Provider,
    TooltipTrigger
} from '../ui/tooltip';

interface DerivedStatProps {
    label: string;
    value: number;
    tooltip?: {
        description: string;
        formula: string;
    };
}

export function DerivedStat({ label, value, tooltip }: DerivedStatProps) {
    return (
        <div className="flex justify-between">
            {tooltip ? (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <span className="cursor-help">{label}:</span>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>{tooltip.description}</p>
                            <p>{tooltip.formula}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            ) : (
                <span>{label}:</span>
            )}
            <span>{value}</span>
        </div>
    );
}
