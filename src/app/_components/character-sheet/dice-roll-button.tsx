'use client';

import { Dice6 as Dice } from 'lucide-react';
import { Button } from '../ui/button';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '../ui/tooltip';
import { DiceInputModal } from './dice-input-modal';
import { RollCheckModal } from './roll-check-modal';
import { useState, useEffect } from 'react';
import {
    MENTAL_SKILLS,
    type Attribute,
    type Character,
    type Skill
} from './types';
import { api } from '../../../trpc/react';

interface DiceRollButtonProps {
    character: Character;
    onCharacterChange: (setter: (prev: Character) => Character) => void;
    selectedAttribute: Attribute | null;
    selectedSkill: Skill | null;
    onResetSelection: () => void;
}

export function DiceRollButton({
    character,
    onCharacterChange,
    selectedAttribute,
    selectedSkill,
    onResetSelection
}: DiceRollButtonProps) {
    const [isRollCheckModalOpen, setIsRollCheckModalOpen] = useState(false);
    const [isDiceInputModalOpen, setIsDiceInputModalOpen] = useState(false);
    const [additionalDice, setAdditionalDice] = useState(0);
    const [rollResults, setRollResults] = useState<number[]>([]);
    const [previousResults, setPreviousResults] = useState<number[]>([]);
    const [previousRollId, setPreviousRollId] = useState<number | undefined>(
        undefined
    );
    const [hasRerolled, setHasRerolled] = useState(false);
    const [isMakingRoll, setIsMakingRoll] = useState(false);
    const [selectedSpecialization, setSelectedSpecialization] = useState<
        string | undefined
    >(undefined);

    useEffect(() => {
        const down = (e: KeyboardEvent) => {
            if (e.key === 'd' && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                handleNewRoll();
            }
        };

        document.addEventListener('keydown', down);
        return () => document.removeEventListener('keydown', down);
    }, []);

    const healthModifier = Math.min(0, -3 + character.health.filled);

    const attributeDiceValue = selectedAttribute
        ? character.attributes[selectedAttribute]
        : 0;

    let skillDiceValue = selectedSkill ? character.skills[selectedSkill] : 0;
    if (selectedSkill && character.skills[selectedSkill] === 0) {
        skillDiceValue = MENTAL_SKILLS.some((ms) => ms === selectedSkill)
            ? -3
            : -1;
    }

    const specializationDiceValue = selectedSpecialization ? 1 : 0;

    const totalDiceToRoll =
        attributeDiceValue +
        skillDiceValue +
        additionalDice +
        specializationDiceValue;

    const isChanceDie = totalDiceToRoll <= 0;
    const rollTitle = selectedAttribute
        ? `${selectedAttribute}${selectedSkill ? ` + ${selectedSkill}` : ''}`
        : selectedAttribute ?? 'Custom Roll';

    const rollMutation = api.diceRolls.performDiceRoll.useMutation();

    const performDiceRoll = async (isWillpowerReroll: boolean) => {
        try {
            setIsMakingRoll(true);
            setIsRollCheckModalOpen(true);
            const delay = new Promise((resolve) => setTimeout(resolve, 500));
            const { results, rollId } = await rollMutation.mutateAsync({
                characterId: character.id,
                diceCount: isWillpowerReroll ? 3 : totalDiceToRoll,
                attribute: selectedAttribute ?? undefined,
                attributeValue: attributeDiceValue,
                skill: selectedSkill ?? undefined,
                skillValue: skillDiceValue,
                additionalDice,
                isWillpowerReroll,
                originalRollId: previousRollId,
                specialization: selectedSpecialization
            })
            await delay;
            if (isWillpowerReroll) {
                setPreviousRollId(undefined);
                setRollResults([...previousResults, ...results]);
            } else {
                setPreviousResults(results);
                setRollResults(results);
                setPreviousRollId(rollId);
            }
        } catch (err) {
            console.error(err);
        } finally {
            setIsMakingRoll(false);
        }
    };

    const handleNewRoll = () => {
        setPreviousRollId(undefined);
        setHasRerolled(false);
        setAdditionalDice(selectedAttribute ? healthModifier : 1);
        setIsDiceInputModalOpen(true);
    };

    const handleReroll = () => {
        if (character.willpower.filled > 0) {
            onCharacterChange((prev) => ({
                ...prev,
                willpower: {
                    ...prev.willpower,
                    filled: prev.willpower.filled - 1,
                    damaged: prev.willpower.damaged + 1
                }
            }));
            performDiceRoll(true);
            setHasRerolled(true);
        }
    };

    const hasAvailableWillpower = character.willpower.filled > 0;

    // Get specializations for the selected skill
    const skillSpecializations = selectedSkill
        ? character.skillSpecializations[selectedSkill]
        : [];

    return (
        <>
            <div className="fixed bottom-0 right-0 z-50 p-6 pb-[calc(env(safe-area-inset-bottom)+1.5rem)]">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                className="shadow-lg rounded-full h-14 w-14 p-0"
                                onClick={handleNewRoll}
                            >
                                {selectedAttribute ? (
                                    <span className="text-sm font-medium flex flex-col leading-3">
                                        <span>
                                            {selectedAttribute.slice(0, 3)}
                                        </span>
                                        {selectedSkill && (
                                            <>
                                                <span>+</span>
                                                <span>
                                                    {selectedSkill.includes(' ')
                                                        ? selectedSkill
                                                              .split(' ')
                                                              .map((w) => w[0])
                                                              .join(' ')
                                                        : selectedSkill.slice(
                                                              0,
                                                              3
                                                          )}
                                                </span>
                                            </>
                                        )}
                                    </span>
                                ) : (
                                    <Dice className="h-6 w-6" />
                                )}
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                            <p>
                                {selectedAttribute
                                    ? `Roll ${selectedAttribute}${
                                          selectedSkill
                                              ? ` + ${selectedSkill}`
                                              : ''
                                      }`
                                    : 'Open dice roller (⌘D)'}
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>

            <RollCheckModal
                isMakingRoll={isMakingRoll}
                rolledDice={totalDiceToRoll}
                isOpen={isRollCheckModalOpen}
                onClose={() => {
                    setIsRollCheckModalOpen(false);
                    setSelectedSpecialization(undefined)
                    onResetSelection();
                }}
                results={rollResults}
                title={rollTitle}
                canReroll={!hasRerolled && hasAvailableWillpower}
                onReroll={handleReroll}
                isChanceDie={isChanceDie}
            />

            <DiceInputModal
                isOpen={isDiceInputModalOpen}
                onClose={() => setIsDiceInputModalOpen(false)}
                onInputChange={setAdditionalDice}
                onSubmit={() => performDiceRoll(false)}
                value={additionalDice}
                baseDicePool={
                    selectedAttribute
                        ? totalDiceToRoll - additionalDice
                        : undefined
                }
                title={
                    selectedAttribute
                        ? `Roll ${selectedAttribute}${
                              selectedSkill ? ` + ${selectedSkill}` : ''
                          }`
                        : 'Custom Roll'
                }
                selectedSkill={selectedSkill}
                specializations={skillSpecializations}
                selectedSpecialization={selectedSpecialization}
                onSpecializationSelect={setSelectedSpecialization}
            />
        </>
    );
}
