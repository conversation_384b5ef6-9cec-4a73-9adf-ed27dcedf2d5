"use client";

import { StatGroup } from "./stat-group";
import { ATTRIBUTES, type Attribute } from "./types";

interface AttributesSectionProps {
    values: Record<Attribute, number>;
    onChange: (attr: Attribute, value: number) => void;
    selectedAttribute: Attribute | null;
    onSelect: (attr: Attribute) => void;
    disabled?: boolean;
}

export function AttributesSection({
    values,
    onChange,
    selectedAttribute,
    onSelect,
    disabled
}: AttributesSectionProps) {
    return (
        <StatGroup<Attribute>
            title="Attributes"
            disabled={disabled}
            stats={ATTRIBUTES.map((a) => a.slice())}
            values={values}
            onChange={onChange}
            selectedStat={selectedAttribute}
            onSelect={onSelect}
        />
    );
}
