'use client';

import { useEffect, useState } from 'react';
import { Button } from '~/app/_components/ui/button';
import {
    Di<PERSON>,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '~/app/_components/ui/dialog';
import { Label } from '../ui/label';
import { noop } from '../../../_lib/utils';
import { calculateRollResult } from '~/_lib/rolls';
import { DiceResults } from '../shared/dice-result';

interface RollCheckModalProps {
    isOpen: boolean;
    onClose: () => void;
    results: number[];
    title: string;
    isChanceDie: boolean;
    canReroll?: boolean;
    onReroll?: () => void;
    isMakingRoll: boolean;
    rolledDice: number;
}

export function RollCheckModal({
    isOpen,
    onClose,
    results,
    isMakingRoll,
    rolledDice,
    title,
    isChanceDie,
    canReroll = false,
    onReroll
}: RollCheckModalProps) {
    const { outcome } = calculateRollResult(results, isChanceDie);

    // Add state to track if reroll has been used
    const [hasRerolled, setHasRerolled] = useState(false);

    // Handle reroll and mark as used
    const handleReroll = () => {
        if (onReroll && !hasRerolled && canReroll) {
            // First set the state to true to disable the button immediately
            setHasRerolled(true);
            // Then call the parent's reroll function
            onReroll();
        }
    };

    // Reset the hasRerolled state when the modal opens with new results
    useEffect(() => {
        if (isOpen) {
            setHasRerolled(false);
        }
    }, [isOpen]);

    return (
        <Dialog open={isOpen} onOpenChange={isMakingRoll ? noop : onClose}>
            <DialogContent className="sm:max-w-md">
                <div className="flex flex-col gap-4">
                    <DialogHeader>
                        <DialogTitle>{title}</DialogTitle>
                    </DialogHeader>

                    <div className="flex flex-col gap-4 relative">
                        <div className="flex flex-col gap-4">
                            <Label>
                                {isMakingRoll ? 'Rolling' : 'Rolled'}{' '}
                                {results.length} dice {title && `(${title})`}
                            </Label>

                            <div
                                className={`flex flex-wrap gap-2 min-h-10 transition-opacity duration-500 justify-center`}
                            >
                                <DiceResults
                                    results={results}
                                    isChanceDie={isChanceDie}
                                    size="md"
                                    isLoading={isMakingRoll}
                                    diceCount={
                                        isChanceDie
                                            ? 1
                                            : !hasRerolled
                                            ? rolledDice
                                            : results.length + 3
                                    }
                                    isWillpowerReroll={hasRerolled}
                                />
                            </div>
                        </div>

                        <div className="flex flex-col gap-1.5">
                            <div
                                className={`text-lg text-center flex items-center justify-center rounded-md border ${
                                    outcome === 'Failure' ||
                                    outcome === 'Critical Failure'
                                        ? 'border-secondary'
                                        : 'border-primary'
                                } font-bold h-10 transition-opacity duration-300 ease-in-out ${
                                    isMakingRoll ? 'opacity-0' : 'opacity-100'
                                }`}
                            >
                                {outcome}
                            </div>
                        </div>
                    </div>

                    <DialogFooter className="flex justify-between">
                        {canReroll && !isChanceDie && onReroll && (
                            <Button
                                variant="secondary"
                                onClick={handleReroll}
                                disabled={
                                    hasRerolled || !canReroll || isMakingRoll
                                }
                                className="mr-auto"
                            >
                                {hasRerolled
                                    ? 'Willpower Used'
                                    : canReroll
                                    ? 'Spend Willpower (+3 dice)'
                                    : 'No Willpower Available'}
                            </Button>
                        )}
                        <Button disabled={isMakingRoll} onClick={onClose}>
                            Close
                        </Button>
                    </DialogFooter>
                </div>
            </DialogContent>
        </Dialog>
    );
}
