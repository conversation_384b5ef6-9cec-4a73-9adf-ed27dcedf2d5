'use client';

import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "~/app/_components/ui/select";
import { ICONS, type Icon } from "./types";
import { Label } from "../ui/label";

interface IconSelectorProps {
    value: Icon | undefined;
    onChange: (value: Icon) => void;
    disabled?: boolean;
}

export function IconSelector({ value, onChange, disabled }: IconSelectorProps) {
    return (
        <div className="flex flex-col gap-1">
            <Label htmlFor="icon">Icon</Label>
            <Select value={value} onValueChange={onChange} disabled={disabled}>
                <SelectTrigger id="icon" className="w-full" disabled={disabled}>
                    <SelectValue placeholder="Select an icon" />
                </SelectTrigger>
                <SelectContent>
                    {ICONS.map((icon) => (
                        <SelectItem key={icon} value={icon}>
                            {icon}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
}