"use client";

import { StatGroup } from "./stat-group";
import { SKILLS, type Skill } from "./types";

interface SkillsSectionProps {
    values: Record<Skill, number>;
    onChange: (skill: Skill, value: number) => void;
    selectedSkill: Skill | null;
    onSelect: (skill: Skill) => void;
    disabled?: boolean;
    specializations?: Partial<Record<Skill, string[]>>;
    onSpecializationChange?: (skill: Skill, specializations: string[]) => void;
}

export function SkillsSection({
    values,
    onChange,
    selectedSkill,
    onSelect,
    disabled,
    specializations,
    onSpecializationChange
}: SkillsSectionProps) {
    return (
        <StatGroup<Skill>
            title="Skills"
            disabled={disabled}
            stats={SKILLS.map((a) => a.slice())}
            values={values}
            onChange={onChange}
            specializations={specializations}
            selectedStat={selectedSkill}
            onSpecializationChange={onSpecializationChange}
            onSelect={onSelect}
        />
    );
}
