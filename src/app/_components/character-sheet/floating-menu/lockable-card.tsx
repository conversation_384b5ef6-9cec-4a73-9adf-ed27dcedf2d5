'use client';

import { <PERSON>, Unlock, X } from 'lucide-react';
import { But<PERSON> } from '../../ui/button';
import { Card, CardContent } from '../../ui/card';
import { useEffect, useRef } from 'react';

interface LockableCardProps {
    isLocked: boolean;
    onLockToggle: () => void;
    onRemove: () => void;
    children: React.ReactNode;
}

export function LockableCard({
    isLocked,
    onLockToggle,
    onRemove,
    children
}: LockableCardProps) {
    const randomId = useRef(Math.random().toString(36).substring(2, 9));

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onLockToggle();
    };

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            // Check if Cmd (Mac) or Ctrl (Windows) + Enter is pressed
            if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
                // Check if the focused element is within this form
                if (
                    document.activeElement?.closest('form')?.id ===
                    randomId.current
                ) {
                    e.preventDefault();
                    onLockToggle();
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [onLockToggle]);

    return (
        <Card className="relative">
            <form id={randomId.current} onSubmit={handleSubmit}>
                <CardContent className="pt-8">
                    <div className="absolute right-2 top-2 flex gap-2">
                        <Button type="submit" variant="ghost" size="icon">
                            {isLocked ? (
                                <Lock className="h-4 w-4" />
                            ) : (
                                <Unlock className="h-4 w-4" />
                            )}
                        </Button>
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={onRemove}
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                    <div className="space-y-2">{children}</div>
                    <button type="submit" className="hidden" />
                </CardContent>
            </form>
        </Card>
    );
}
