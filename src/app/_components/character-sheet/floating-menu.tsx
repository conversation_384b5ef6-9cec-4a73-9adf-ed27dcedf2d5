'use client';

import { Star, Package, StickyNote, BookOpen } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Sheet, SheetTrigger } from '../ui/sheet';
import { useState, useEffect } from 'react';
import { MeritPanel } from './floating-menu/merit-panel';
import { InventoryPanel } from './floating-menu/inventory-panel';
import { NotesPanel } from './floating-menu/notes-panel';
import { LorePanel } from './floating-menu/lore-panel';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '../ui/tooltip';

interface FloatingMenuProps {
    characterId: number;
    merits: Array<{ name: string; value: number; description: string }>;
    inventory: Array<{ name: string; description: string; modifiers: string }>;
    ambitions: string[];
    onMeritsChange: (
        merits: Array<{ name: string; value: number; description: string }>
    ) => void;
    onInventoryChange: (
        inventory: Array<{
            name: string;
            description: string;
            modifiers: string;
        }>
    ) => void;
    onAmbitionsChange: (ambitions: string[]) => void;
}

export function FloatingMenu({
    merits,
    inventory,
    ambitions,
    characterId,
    onMeritsChange,
    onInventoryChange,
    onAmbitionsChange
}: FloatingMenuProps) {
    const [activePanel, setActivePanel] = useState<
        'merits' | 'inventory' | 'notes' | 'lore' | null
    >(null);
    const [unlockedMeritIndex, setUnlockedMeritIndex] = useState<number | null>(
        null
    );
    const [unlockedInventoryIndex, setUnlockedInventoryIndex] = useState<
        number | null
    >(null);

    const handlePanelChange = (
        open: boolean,
        panel: 'merits' | 'inventory' | 'notes' | 'lore' | null
    ) => {
        if (!open) {
            if (panel === 'merits') {
                setUnlockedMeritIndex(null);
            } else if (panel === 'inventory') {
                setUnlockedInventoryIndex(null);
            }
        }
        setActivePanel(open ? panel : null);
    };

    useEffect(() => {
        const down = (e: KeyboardEvent) => {
            if (e.metaKey || e.ctrlKey) {
                switch (e.key.toLowerCase()) {
                    case 'm':
                        e.preventDefault();
                        handlePanelChange(activePanel !== 'merits', 'merits');
                        break;
                    case 'i':
                        e.preventDefault();
                        handlePanelChange(
                            activePanel !== 'inventory',
                            'inventory'
                        );
                        break;
                    case 'p':
                        e.preventDefault();
                        handlePanelChange(activePanel !== 'notes', 'notes');
                        break;
                    case 'l':
                        e.preventDefault();
                        handlePanelChange(activePanel !== 'lore', 'lore');
                        break;
                }
            }
        };

        document.addEventListener('keydown', down);
        return () => document.removeEventListener('keydown', down);
    }, [activePanel]);

    return (
        <div className="fixed right-6 max-md:top-32 top-20 flex flex-col gap-2">
            <TooltipProvider>
                <Sheet
                    open={activePanel === 'merits'}
                    onOpenChange={(open) => handlePanelChange(open, 'merits')}
                >
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <SheetTrigger asChild>
                                <Button
                                    size="icon"
                                    variant="outline"
                                    className="rounded-full"
                                >
                                    <Star className="h-4 w-4" />
                                </Button>
                            </SheetTrigger>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                            <p>Merits (⌘M)</p>
                        </TooltipContent>
                    </Tooltip>
                    <MeritPanel
                        merits={merits}
                        unlockedMeritIndex={unlockedMeritIndex}
                        onMeritsChange={onMeritsChange}
                        setUnlockedMeritIndex={setUnlockedMeritIndex}
                    />
                </Sheet>

                <Sheet
                    open={activePanel === 'inventory'}
                    onOpenChange={(open) =>
                        handlePanelChange(open, 'inventory')
                    }
                >
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <SheetTrigger asChild>
                                <Button
                                    size="icon"
                                    variant="outline"
                                    className="rounded-full"
                                >
                                    <Package className="h-4 w-4" />
                                </Button>
                            </SheetTrigger>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                            <p>Inventory (⌘I)</p>
                        </TooltipContent>
                    </Tooltip>
                    <InventoryPanel
                        inventory={inventory}
                        unlockedInventoryIndex={unlockedInventoryIndex}
                        onInventoryChange={onInventoryChange}
                        setUnlockedInventoryIndex={setUnlockedInventoryIndex}
                    />
                </Sheet>

                <Sheet
                    open={activePanel === 'notes'}
                    onOpenChange={(open) => handlePanelChange(open, 'notes')}
                >
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <SheetTrigger asChild>
                                <Button
                                    size="icon"
                                    variant="outline"
                                    className="rounded-full"
                                >
                                    <StickyNote className="h-4 w-4" />
                                </Button>
                            </SheetTrigger>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                            <p>Player Notes (⌘P)</p>
                        </TooltipContent>
                    </Tooltip>
                    <NotesPanel
                        characterId={characterId}
                        ambitions={ambitions}
                        onAmbitionsChange={onAmbitionsChange}
                    />
                </Sheet>

                <Sheet
                    open={activePanel === 'lore'}
                    onOpenChange={(open) => handlePanelChange(open, 'lore')}
                >
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <SheetTrigger asChild>
                                <Button
                                    size="icon"
                                    variant="outline"
                                    className="rounded-full"
                                >
                                    <BookOpen className="h-4 w-4" />
                                </Button>
                            </SheetTrigger>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                            <p>Lore (⌘L)</p>
                        </TooltipContent>
                    </Tooltip>
                    <LorePanel />
                </Sheet>
            </TooltipProvider>
        </div>
    );
}
