'use client'

import { noop } from '~/_lib/utils'
import { FormField } from './form-field'
import { ICONS, type Character, type Icon } from './types'
import { SafeImage } from '../safe-image'
import { Di<PERSON>, DialogContent, DialogTitle, DialogFooter } from '../ui/dialog'
import { useState, useRef, useEffect } from 'react'
import { api } from '~/trpc/react'
import { useSession } from 'next-auth/react'
import { Button } from '../ui/button'
import { Upload, ContactRound, Wand2 } from 'lucide-react'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { Spinner } from '../ui/spinner'
import { IconSelector } from './icon-selector'
import { Textarea } from '../ui/textarea'

interface CharacterInfoSectionProps {
    character: {
        id: number
        name: string
        virtue: string
        vice: string
        concept: string
        icon: string
        imageUrl?: string
    }
    userName: string
    onFieldChange: (field: keyof Character, value: string) => void
    disabled?: boolean
}

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB in bytes

export function CharacterInfoSection({
    character,
    userName,
    disabled,
    onFieldChange
}: CharacterInfoSectionProps) {
    const { data: session } = useSession()
    const router = useRouter()
    const searchParams = useSearchParams()
    const pathname = usePathname()
    const isGenerating = searchParams.has('waitForImage')

    const regenerateMutation = api.character.regeneratePortrait.useMutation({
        onMutate: () => {
            // Close the modal immediately when starting regeneration
            setIsPortraitModalOpen(false)
            // Add waitForImage param to trigger polling
            const params = new URLSearchParams(searchParams)
            params.set('waitForImage', 'true')
            router.replace(`${pathname}?${params.toString()}`)
        },
        onError: () => {
            // Remove waitForImage param on error
            const params = new URLSearchParams(searchParams)
            params.delete('waitForImage')
            router.replace(`${pathname}?${params.toString()}`)
        }
    })
    const [isPortraitModalOpen, setIsPortraitModalOpen] = useState(false)
    const [isPromptModalOpen, setIsPromptModalOpen] = useState(false)
    const [imagePrompt, setImagePrompt] = useState('')
    const fileInputRef = useRef<HTMLInputElement>(null)
    const uploadMutation = api.character.getUploadUrl.useMutation()
    const updateCharacterImageUrlMutation =
        api.character.updateImageUrl.useMutation()

    const handleImageUpload = async (file: File) => {
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file')
            return
        }

        if (file.size > MAX_FILE_SIZE) {
            alert('Image must be under 5MB')
            return
        }

        try {
            // Get presigned URL
            const { uploadUrl, imageUrl } = await uploadMutation.mutateAsync({
                characterId: character.id,
                contentType: file.type,
                fileSize: file.size
            })

            // Upload directly to MinIO
            const response = await fetch(uploadUrl, {
                method: 'PUT',
                body: file,
                headers: {
                    'Content-Type': file.type
                }
            })

            if (!response.ok) {
                throw new Error('Upload failed')
            }

            // Update both the UI and database
            onFieldChange('imageUrl', imageUrl)
            await updateCharacterImageUrlMutation.mutateAsync({
                id: character.id,
                imageUrl: imageUrl
            })
        } catch (error) {
            console.error('Failed to upload image:', error)
            alert('Failed to upload image. Please try again.')
        }
    }

    const handleUploadButtonClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click()
        }
    }

    const { data: imageGenerationPrompt, isFetching: isFetchingImagePrompt } =
        api.character.getImagePrompt.useQuery(
            {
                characterId: character.id
            },
            { enabled: session?.user?.isAdmin && !!character.id }
        )

    return (
        <>
            <div className="grid gap-6 md:grid-cols-3">
                <div className="flex flex-col gap-2">
                    <FormField
                        label="Character Name"
                        autoFocus={character.name === ''}
                        value={character.name}
                        disabled={disabled}
                        onChange={(value) => onFieldChange('name', value)}
                    />
                    <IconSelector
                        disabled={disabled}
                        value={
                            ICONS.some((i) => i === character.icon)
                                ? (character.icon as Icon)
                                : undefined
                        }
                        onChange={(value) => onFieldChange('icon', value)}
                    />
                    <FormField
                        label="Player"
                        value={userName}
                        disabled
                        onChange={noop}
                    />
                </div>
                <div className="flex flex-col gap-2">
                    <FormField
                        label="Concept"
                        disabled={disabled}
                        value={character.concept}
                        onChange={(value) => onFieldChange('concept', value)}
                    />
                    <FormField
                        label="Virtue"
                        disabled={disabled}
                        value={character.virtue}
                        onChange={(value) => onFieldChange('virtue', value)}
                    />
                    <FormField
                        label="Vice"
                        disabled={disabled}
                        value={character.vice}
                        onChange={(value) => onFieldChange('vice', value)}
                    />
                </div>
                <div className="flex flex-col gap-2 md:col-start-3 md:col-span-1 max-md:order-first justify-center">
                    <input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        accept="image/jpeg,image/png,image/webp"
                        onChange={(e) => {
                            const file = e.target.files?.[0]
                            if (file) {
                                if (file.size > MAX_FILE_SIZE) {
                                    alert('Image must be under 5MB')
                                    e.target.value = '' // Clear the input
                                    return
                                }
                                handleImageUpload(file)
                            }
                        }}
                    />
                    <div className="flex justify-center relative">
                        {character.imageUrl ? (
                            <SafeImage
                                src={character.imageUrl}
                                alt={`Portrait of ${character.name}`}
                                className="rounded-lg shadow-lg h-[178px] w-[178px] object-cover cursor-pointer hover:opacity-90 transition-opacity"
                                width={178}
                                height={178}
                                onClick={(isError) =>
                                    isError
                                        ? fileInputRef.current?.click()
                                        : setIsPortraitModalOpen(true)
                                }
                            />
                        ) : (
                            <div
                                className="text-4xl text-muted-foreground cursor-pointer hover:opacity-90 transition-opacity"
                                onClick={() => setIsPortraitModalOpen(true)}
                            >
                                <ContactRound
                                    strokeWidth={1}
                                    className="h-20 w-20 text-muted-foreground"
                                />
                            </div>
                        )}
                        {isGenerating && (
                            <div className="absolute inset-0 bg-background/50 rounded-lg flex items-center justify-center">
                                <Spinner className="text-primary" size="lg" />
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <Dialog
                open={isPortraitModalOpen}
                onOpenChange={setIsPortraitModalOpen}
            >
                <DialogContent className="max-w-3xl">
                    <DialogTitle className="flex justify-between items-center">
                        Character Portrait
                        <div className="flex gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleUploadButtonClick}
                            >
                                <Upload className="h-4 w-4 mr-2" />
                                {character.imageUrl ? 'Replace' : 'Upload'}
                            </Button>
                            {session?.user?.isAdmin && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={async () => {
                                        if (!isFetchingImagePrompt) {
                                            setIsPortraitModalOpen(false)
                                            setImagePrompt(
                                                imageGenerationPrompt ?? ''
                                            )
                                            setIsPromptModalOpen(true)
                                        }
                                    }}
                                    disabled={regenerateMutation.isPending}
                                >
                                    <Wand2 className="h-4 w-4 mr-2" />
                                    {regenerateMutation.isPending
                                        ? 'Generating...'
                                        : 'Edit Prompt'}
                                </Button>
                            )}
                        </div>
                    </DialogTitle>
                    {character.imageUrl && (
                        <div className="relative w-full aspect-square">
                            <SafeImage
                                src={character.imageUrl}
                                alt={`Portrait of ${character.name}`}
                                className="rounded-lg object-contain"
                                fill
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 70vw, 50vw"
                                priority
                            />
                        </div>
                    )}
                </DialogContent>
            </Dialog>

            <Dialog
                open={isPromptModalOpen}
                onOpenChange={setIsPromptModalOpen}
            >
                <DialogContent className="sm:max-w-md">
                    <DialogTitle>Edit Generation Prompt</DialogTitle>
                    <Textarea
                        value={imagePrompt}
                        onChange={(e) => setImagePrompt(e)}
                        placeholder="Describe the character's appearance..."
                        className="min-h-[150px]"
                    />
                    <DialogFooter>
                        <Button
                            onClick={() => {
                                setIsPromptModalOpen(false)
                                regenerateMutation.mutate({
                                    characterId: character.id,
                                    prompt: imagePrompt
                                })
                            }}
                            disabled={
                                regenerateMutation.isPending ||
                                !imagePrompt.trim()
                            }
                        >
                            {regenerateMutation.isPending
                                ? 'Generating...'
                                : 'Regenerate'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}
