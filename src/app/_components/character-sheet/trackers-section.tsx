"use client";

import { StatTracker } from "./stat-tracker";
import type { TrackerState } from "./types";

interface TrackersSectionProps {
    health: TrackerState;
    maxHealth: number;
    stamina: number;
    willpower: TrackerState;
    maxWillpower: number;
    onHealthChange: (value: TrackerState) => void;
    onWillpowerChange: (value: TrackerState) => void;
}

export function TrackersSection({
    health,
    maxHealth,
    willpower,
    stamina,
    maxWillpower,
    onHealthChange,
    onWillpowerChange
}: TrackersSectionProps) {
    return (
        <div className="grid gap-4 md:grid-cols-2">
            <StatTracker
                label="Health"
                customLabel={
                    health.damaged > stamina ||
                    health.filled + health.damaged < maxHealth ? (
                        <span>
                            Health{' '}
                            <sup className="text-red-800">(Beaten Down)</sup>
                        </span>
                    ) : undefined
                }
                dangerThreshold={3}
                value={health}
                maxValue={maxHealth}
                totalDots={10}
                allowDamage={true}
                onChange={onHealthChange}
                tooltip={{
                    description: 'Physical health and injury status',
                    formula: 'Stamina + Size (5 by default)'
                }}
            />

            <StatTracker
                label="Willpower"
                value={willpower}
                maxValue={maxWillpower}
                totalDots={10}
                allowDamage={true}
                onChange={onWillpowerChange}
                tooltip={{
                    description: 'Mental fortitude and determination',
                    formula: 'Resolve + Composure'
                }}
            />
        </div>
    );
}
