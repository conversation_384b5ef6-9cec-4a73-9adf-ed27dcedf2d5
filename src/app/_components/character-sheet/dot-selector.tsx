"use client";

import { type KeyboardEvent, useEffect, useRef, useState } from "react";
import type { TrackerState } from "./types";

interface DotSelectorProps<T extends TrackerState | number> {
    value: T;
    max: number;
    totalDots?: number;
    onChange: (value: T) => void;
    allowDamage?: boolean;
    dangerThreshold?: number;
    disabled?: boolean;
}

export function DotSelector<T extends TrackerState | number>({
    value,
    max,
    totalDots = 5,
    onChange,
    dangerThreshold,
    disabled: isComponentDisabled,
    allowDamage = false
}: DotSelectorProps<T>) {
    // Refs for the dots
    const dotRefs = useRef<(HTMLSpanElement | null)[]>([]);
    const containerRef = useRef<HTMLDivElement>(null);

    // Initialize the refs array
    useEffect(() => {
        dotRefs.current = dotRefs.current.slice(0, totalDots);
    }, [totalDots]);

    useEffect(() => {
        if (typeof value === 'number') {
            if (value > max) {
                onChange(max as T);
            }
        } else {
            const totalValue = value.filled + value.damaged;
            if (totalValue > max) {
                const filled = Math.min(max, value.filled);
                onChange({
                    filled,
                    damaged: max - filled
                } as T);
            }
        }
    }, [value, max, onChange]);

    // Extract filled and damaged counts based on value type
    const filledCount = typeof value === 'number' ? value : value.filled;
    const damagedCount = typeof value === 'number' ? 0 : value.damaged;
    const totalFilledAndDamaged = filledCount + damagedCount;

    const handleDotClick = (dotValue: number) => {
        if (dotValue > max) return; // Don't allow clicking disabled dots

        if (allowDamage && typeof value !== 'number') {
            // Determine if we're clicking on a filled, damaged, or empty dot
            if (dotValue <= filledCount) {
                // Clicking on a filled dot
                // Make this dot and all filled dots to the right damaged
                const newFilled = dotValue - 1;
                const newDamaged = filledCount - newFilled + damagedCount;

                onChange({
                    filled: newFilled,
                    damaged: newDamaged
                } as T);
            } else if (dotValue <= totalFilledAndDamaged) {
                // Clicking on a damaged dot
                // Make this dot and all damaged dots to the right empty
                const damagedPosition = dotValue - filledCount;
                const newDamaged = damagedPosition - 1;

                onChange({
                    filled: filledCount,
                    damaged: newDamaged
                } as T);
            } else {
                // Clicking on an empty dot - fill it
                // This will make all dots up to this one filled
                onChange({
                    filled: dotValue,
                    damaged: 0
                } as T);
            }
        } else {
            // Standard behavior for attributes and skills
            onChange((dotValue === filledCount ? dotValue - 1 : dotValue) as T);
        }
    };

    const handleKeyDown = (
        e: KeyboardEvent<HTMLSpanElement>,
        dotValue: number
    ) => {
        if (dotValue > max) return; // Don't handle keyboard events for disabled dots

        switch (e.key) {
            case 'Enter':
            case ' ':
                e.preventDefault();
                handleDotClick(dotValue);
                break;
            case 'ArrowLeft':
                e.preventDefault();
                if (dotValue > 1) {
                    const prevDot = dotRefs.current[dotValue - 2];
                    prevDot?.focus();
                }
                break;
            case 'ArrowRight':
                e.preventDefault();
                if (dotValue < totalDots) {
                    const nextDot = dotRefs.current[dotValue];
                    nextDot?.focus();
                }
                break;
            case 'Home': {
                e.preventDefault();
                const firstDot = dotRefs.current[0];
                firstDot?.focus();
                break;
            }
            case 'End': {
                e.preventDefault();
                const lastDot = dotRefs.current[totalDots - 1];
                lastDot?.focus();
                break;
            }
            // Remove the Tab case - let the browser handle natural tab order
        }
    };

    return (
        <div
            ref={containerRef}
            className="flex space-x-1"
            aria-label="Dot selector"
            role="group"
        >
            {[...Array(totalDots).keys()].map((dot, i) => {
                const dotValue = dot + 1;
                const isDotDisabled = dotValue > max;

                // Determine dot state based on value type
                let isActive = false;
                let isDamaged = false;

                if (typeof value === 'number') {
                    isActive = dotValue <= value;
                } else {
                    isActive = dotValue <= value.filled;
                    isDamaged =
                        !isActive && dotValue <= value.filled + value.damaged;
                }

                return (
                    <span
                        key={dotValue}
                        ref={(el) => {
                            dotRefs.current[dot] = el;
                        }}
                        role="button"
                        tabIndex={isDotDisabled ? -1 : dot === 0 ? 0 : -1} // Only first dot is tab-focusable
                        className={`size-4 rounded-full border border-foreground transition-colors
                            ${isActive ? 'bg-foreground' : 'bg-transparent'}
                            ${
                                isDotDisabled || isComponentDisabled
                                    ? 'cursor-not-allowed opacity-70'
                                    : 'cursor-pointer'
                            }
                            ${isDotDisabled ? '!opacity-20' : ''}
                            ${
                                isDamaged
                                    ? 'bg-[linear-gradient(45deg,transparent_46%,currentColor_46%,currentColor_54%,transparent_54%),linear-gradient(-45deg,transparent_46%,currentColor_46%,currentColor_54%,transparent_54%)] bg-center bg-no-repeat'
                                    : ''
                            }
                            ${
                                dangerThreshold &&
                                i < dangerThreshold &&
                                (isDamaged || !isActive)
                                    ? '!border-dashed !border-current text-red-800'
                                    : ''
                            }
                            outline-none
                            focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50
                            aria-invalid:border-destructive aria-invalid:ring-destructive/20 
                            dark:aria-invalid:ring-destructive/40`}
                        onClick={() => {
                            if (!isDotDisabled && !isComponentDisabled) {
                                handleDotClick(dotValue);
                            }
                        }}
                        onKeyDown={(e) => handleKeyDown(e, dotValue)}
                    />
                );
            })}
        </div>
    );
}
