"use client";

import type React from "react";
import { Minus, Plus } from 'lucide-react';
import { useState } from 'react';

import { Button } from '~/app/_components/ui/button';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogFooter,
    Di<PERSON>Header,
    DialogTitle
} from '~/app/_components/ui/dialog';
import { Label } from '~/app/_components/ui/label';
import type { Skill } from './types';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '~/app/_components/ui/select';

interface DiceInputModalProps {
    isOpen: boolean;
    onClose: () => void;
    value: number;
    onSubmit: () => void;
    onInputChange: (value: number) => void;
    baseDicePool?: number;
    title?: string;
    selectedSkill?: Skill | null;
    specializations?: string[];
    selectedSpecialization?: string;
    onSpecializationSelect: (specialization: string | undefined) => void;
}

export function DiceInputModal({
    isOpen,
    onClose,
    value,
    onSubmit,
    onInputChange,
    baseDicePool,
    title = 'Custom Dice Roll',
    selectedSkill,
    specializations = [],
    selectedSpecialization,
    onSpecializationSelect
}: DiceInputModalProps) {
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit();
        onClose();
    };

    const handleSpecializationSelect = (spec: string | undefined) => {
        onSpecializationSelect?.(spec);
    };

    const isCustomRoll = baseDicePool === undefined;
    const MIN_VALUE = isCustomRoll ? 0 : -5;
    const MAX_VALUE = isCustomRoll ? 10 : 5;

    const handleDecrement = () => {
        if (value > MIN_VALUE) {
            onInputChange(value - 1);
        }
    };

    const handleIncrement = () => {
        if (value < MAX_VALUE) {
            onInputChange(value + 1);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit}>
                    <div className="py-4">
                        <div className="space-y-4">
                            <div className="flex flex-col space-y-1.5">
                                <Label>
                                    {isCustomRoll
                                        ? 'Number of Dice'
                                        : 'Add Modifier'}
                                </Label>
                                <div className="flex items-center justify-center gap-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="icon"
                                        onClick={handleDecrement}
                                        disabled={value <= MIN_VALUE}
                                    >
                                        <Minus className="h-4 w-4" />
                                    </Button>

                                    <div
                                        className={`w-12 text-center ${
                                            isCustomRoll && !value
                                                ? 'text-sm leading-3.5'
                                                : 'text-lg'
                                        } font-semibold select-none`}
                                    >
                                        {isCustomRoll
                                            ? value === 0
                                                ? 'Chance Die'
                                                : value
                                            : value > 0
                                            ? `+${value}`
                                            : value}
                                    </div>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="icon"
                                        onClick={handleIncrement}
                                        disabled={value >= MAX_VALUE}
                                    >
                                        <Plus className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>

                            {selectedSkill && specializations.length > 0 && (
                                <div className="flex flex-col space-y-1.5">
                                    <Label>Specialization Die</Label>
                                    <div className="flex flex-wrap gap-2">
                                        {specializations
                                            .filter(Boolean)
                                            .map((spec) => (
                                                <Button
                                                    type="button"
                                                    key={spec}
                                                    variant={
                                                        selectedSpecialization ===
                                                        spec
                                                            ? 'default'
                                                            : 'ghost'
                                                    }
                                                    onClick={() =>
                                                        handleSpecializationSelect(
                                                            selectedSpecialization ===
                                                                spec
                                                                ? undefined
                                                                : spec
                                                        )
                                                    }
                                                    size="sm"
                                                >
                                                    {spec}
                                                </Button>
                                            ))}
                                    </div>
                                </div>
                            )}

                            {baseDicePool !== undefined && (
                                <div className="flex flex-col space-y-1.5">
                                    <Label>Final Dice Pool</Label>
                                    <div className="text-lg font-semibold">
                                        {baseDicePool + value > 0
                                            ? baseDicePool + value
                                            : 'Chance Die'}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                        >
                            Cancel
                        </Button>
                        <Button type="submit">Roll Dice</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
