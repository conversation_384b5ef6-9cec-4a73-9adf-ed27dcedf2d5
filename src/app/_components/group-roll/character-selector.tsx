'use client';

import { Checkbox } from '../ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { ScrollArea } from '../ui/scroll-area';

interface CharacterSelectorProps {
    characters: Array<{ id: number; content: string; imageUrl?: string | null }>;
    selectedIds: number[];
    onSelectionChange: (ids: number[]) => void;
}

export function CharacterSelector({ characters, selectedIds, onSelectionChange }: CharacterSelectorProps) {
    const handleToggle = (id: number) => {
        if (selectedIds.includes(id)) {
            onSelectionChange(selectedIds.filter(charId => charId !== id));
        } else {
            onSelectionChange([...selectedIds, id]);
        }
    };
    
    return (
        <Card>
            <CardHeader>
                <CardTitle>Select Characters</CardTitle>
            </CardHeader>
            <CardContent>
                <ScrollArea className="h-60 pr-4">
                    <div className="space-y-2">
                        {characters.map(character => {
                            const characterData = JSON.parse(character.content);
                            return (
                                <div key={character.id} className="flex items-center space-x-3 p-2 rounded hover:bg-muted">
                                    <Checkbox 
                                        id={`char-${character.id}`}
                                        checked={selectedIds.includes(character.id)}
                                        onCheckedChange={() => handleToggle(character.id)}
                                    />
                                    {character.imageUrl && (
                                        <img 
                                            src={character.imageUrl} 
                                            alt={characterData.name || 'Character'} 
                                            className="w-8 h-8 rounded-full object-cover"
                                        />
                                    )}
                                    <label 
                                        htmlFor={`char-${character.id}`}
                                        className="text-sm font-medium leading-none cursor-pointer"
                                    >
                                        {characterData.name || 'Unnamed Character'}
                                    </label>
                                </div>
                            );
                        })}
                    </div>
                </ScrollArea>
            </CardContent>
        </Card>
    );
}