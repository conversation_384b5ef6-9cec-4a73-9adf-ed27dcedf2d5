'use client';

import { useState } from 'react';
import { api } from '../../../trpc/react';
import { CharacterSelector } from './character-selector';
import { GroupRollControls } from './group-roll-controls';
import { GroupRollResults } from './group-roll-results';
import { type Attribute, type Skill } from '../character-sheet/types';
import { useSession } from 'next-auth/react'

export function GroupRollContainer() {
    const session = useSession()
    const { data } = api.character.getList.useQuery()
    const isNonAdminCharacter = (c: NonNullable<typeof data>[number]) =>
        c.createdById !== session.data?.user.id
    const characters =
        data
            ?.sort((a, b) =>
                JSON.parse(a.content).name.localeCompare(
                    JSON.parse(b.content).name
                )
            )
            ?.sort((a, b) =>
                isNonAdminCharacter(a) ? (isNonAdminCharacter(b) ? 0 : -1) : 1
            ) ?? []

    const [selectedCharacterIds, setSelectedCharacterIds] = useState<number[]>(
        characters?.filter(isNonAdminCharacter).map((c) => c.id) ?? []
    )
    const [selectedAttribute, setSelectedAttribute] =
        useState<Attribute | null>(null)
    const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null)
    const [additionalDice, setAdditionalDice] = useState(0)
    const [isDualAttributeMode, setIsDualAttributeMode] = useState(false)
    const [rollResults, setRollResults] = useState<
        | {
              characterId: number
              characterName: any
              results: number[]
              rollId: number
              isChanceDie: boolean
          }[]
        | null
    >(null)

    const groupRollMutation = api.diceRolls.performGroupDiceRoll.useMutation()

    const handleRoll = async () => {
        if (selectedCharacterIds.length === 0) return

        const results = await groupRollMutation.mutateAsync({
            characterIds: selectedCharacterIds,
            attribute: selectedAttribute ?? undefined,
            skill: selectedSkill ?? undefined,
            additionalDice,
            isDualAttributeMode
        })

        setRollResults(results)
    }

    return (
        <div className="space-y-8">
            <CharacterSelector
                characters={characters ?? []}
                selectedIds={selectedCharacterIds}
                onSelectionChange={setSelectedCharacterIds}
            />

            <GroupRollControls
                selectedAttribute={selectedAttribute}
                onAttributeChange={setSelectedAttribute}
                selectedSkill={selectedSkill}
                onSkillChange={setSelectedSkill}
                additionalDice={additionalDice}
                onAdditionalDiceChange={setAdditionalDice}
                onRoll={handleRoll}
                isRolling={groupRollMutation.isPending}
                disabled={selectedCharacterIds.length === 0}
                isDualAttributeMode={isDualAttributeMode}
                onDualAttributeModeChange={setIsDualAttributeMode}
            />

            {rollResults && <GroupRollResults results={rollResults} />}
        </div>
    )
}
