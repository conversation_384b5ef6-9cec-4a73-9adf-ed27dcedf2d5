'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { DiceResults } from '../shared/dice-result';
import { calculateRollResult } from '../../../_lib/rolls';

interface GroupRollResultsProps {
    results: {
        characterId: number
        characterName: any
        results: number[]
        rollId: number
        isChanceDie: boolean
        attribute?: string
        skill?: string
        attributeValue?: number
        skillValue?: number
    }[]
}

export function GroupRollResults({ results }: GroupRollResultsProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Roll Results</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {results.map((result) => {
                        const { successCount, outcome } = calculateRollResult(
                            result.results,
                            result.isChanceDie
                        )

                        let rollTypeDisplay = 'Custom Roll'
                        if (result.attribute) {
                            if (result.skill) {
                                rollTypeDisplay = `${result.attribute} (${result.attributeValue}) + ${result.skill} (${result.skillValue})`
                            } else {
                                rollTypeDisplay = `${result.attribute} (${result.attributeValue})`
                            }
                        }

                        return (
                            <div
                                key={result.rollId}
                                className="border rounded-lg p-4"
                            >
                                <div className="flex justify-between items-center mb-2">
                                    <div>
                                        <h3 className="font-medium">
                                            {result.characterName}
                                        </h3>
                                        <p className="text-xs text-muted-foreground">
                                            {rollTypeDisplay}
                                        </p>
                                    </div>
                                    <span className="text-sm font-semibold text-right">
                                        {successCount}{' '}
                                        {successCount === 1
                                            ? 'Success'
                                            : 'Successes'}
                                        <br />
                                        {outcome}
                                    </span>
                                </div>
                                <DiceResults
                                    results={result.results}
                                    isChanceDie={result.isChanceDie}
                                />
                            </div>
                        )
                    })}
                </div>
            </CardContent>
        </Card>
    )
}
