'use client';

import Image, { type ImageProps } from 'next/image';
import { useState } from 'react';
import { cn } from '../../_lib/utils';
import React from 'react';
import { ContactRound } from 'lucide-react';

type SafeImageProps = Omit<ImageProps, 'onError' | 'onClick'> & {
    fallbackContent?: React.ReactNode;
    className?: string;
    onClick?: (isError: boolean) => void;
};

export const SafeImage = React.memo(function SafeImage({
    fallbackContent,
    className,
    onClick,
    ...props
}: SafeImageProps) {
    const [error, setError] = useState(false);

    if (error) {
        return (
            <div
                className={cn(
                    'flex items-center justify-center bg-accent/50 rounded-lg',
                    // Preserve the original image dimensions and clickability
                    className,
                    onClick &&
                        'cursor-pointer hover:opacity-90 transition-opacity'
                )}
                onClick={() => onClick?.(true)}
                style={{
                    // Maintain aspect ratio if height/width were provided
                    aspectRatio:
                        props.width && props.height
                            ? `${props.width}/${props.height}`
                            : undefined,
                    // If using fill prop, ensure full size
                    width: props.fill ? '100%' : props.width,
                    height: props.fill ? '100%' : props.height
                }}
            >
                {fallbackContent || (
                    <div className="text-4xl text-muted-foreground">
                        <ContactRound
                            strokeWidth={1}
                            className="h-20 w-20 text-muted-foreground"
                        />
                    </div>
                )}
            </div>
        );
    }

    return (
        <Image
            {...props}
            className={className}
            onClick={() => onClick?.(false)}
            onError={() => setError(true)}
        />
    );
});