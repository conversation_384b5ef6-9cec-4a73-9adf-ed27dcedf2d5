'use client';

import { useState } from 'react';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription
} from './ui/dialog';
import { Button } from './ui/button';
import { Dices } from 'lucide-react';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from './ui/tooltip'
import { GroupRollContainer } from './group-roll/group-roll-container';

export function GroupRollModal() {
    const [isOpen, onToggle] = useState(false)
    return (
        <>
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div className="relative">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onToggle(true)}
                            >
                                <Dices className="w-4 h-4 mr-2" />
                                Group Roll
                            </Button>
                        </div>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Group rolls</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>

            <Dialog
                open={isOpen}
                onOpenChange={(open) => !open && onToggle(false)}
            >
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            Recent Roll History
                        </DialogTitle>
                        <DialogDescription>
                            Roll dice for several characters
                        </DialogDescription>
                    </DialogHeader>
                    <GroupRollContainer />
                </DialogContent>
            </Dialog>
        </>
    )
}
