'use client'

import { useEffect, useState } from 'react'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON>alogHeader,
    DialogTitle,
    DialogDescription
} from './ui/dialog'
import { api } from '~/trpc/react'
import { formatDateTime } from '~/_lib/format'
import { calculateRollResult } from '~/_lib/rolls'
import { useSession } from 'next-auth/react'
import { Button } from './ui/button'
import { Pause, Play, History } from 'lucide-react'
import { useUserNames } from '../_contexts/user-names-context'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from './ui/tooltip'
import { DiceResults } from './shared/dice-result'

const LAST_OPENED_KEY = 'rollHistoryLastOpened'
const PAUSE_STATE_KEY = 'rollHistoryPaused'

export function RollHistoryModal() {
    const [isOpen, onToggle] = useState(false)
    const session = useSession()
    const userNames = useUserNames()
    const [lastOpenedTime, setLastOpenedTime] = useState<Date | null>(null)
    const [isPaused, setIsPaused] = useState(true)

    // Initialize lastOpenedTime and pause state from localStorage after mount
    useEffect(() => {
        const stored = localStorage.getItem(LAST_OPENED_KEY)
        setLastOpenedTime(stored ? new Date(stored) : new Date(0))

        const storedPauseState = localStorage.getItem(PAUSE_STATE_KEY)
        setIsPaused(
            storedPauseState === null ? true : storedPauseState === 'true'
        )
    }, [])

    // Update localStorage when pause state changes
    useEffect(() => {
        localStorage.setItem(PAUSE_STATE_KEY, isPaused.toString())
    }, [isPaused])

    const { data: rolls } = api.diceRolls.getRecentRolls.useQuery(undefined, {
        refetchInterval: isPaused ? false : 2000
    })

    const isOtherPlayerRoll = (roll: NonNullable<typeof rolls>[number]) =>
        roll.playerId !== session.data?.user.id

    // Update last opened time when modal opens
    useEffect(() => {
        if (isOpen && typeof window !== 'undefined') {
            return () => {
                const now = new Date()
                setLastOpenedTime(now)
                localStorage.setItem(LAST_OPENED_KEY, now.toISOString())
            }
        }
    }, [isOpen])

    // Don't render anything until lastOpenedTime is initialized
    if (lastOpenedTime === null) {
        return null
    }

    // Filter out current user's rolls and count new ones
    const otherPlayersRolls = rolls?.filter(isOtherPlayerRoll) ?? []

    const newRollsCount = otherPlayersRolls.filter(
        (roll) => new Date(roll.date) > lastOpenedTime
    ).length

    return (
        <>
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div className="relative">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onToggle(true)}
                            >
                                <History className="w-4 h-4 mr-2" />
                                Roll History
                            </Button>
                            {/* Badge showing new rolls count */}
                            {!isOpen && newRollsCount > 0 ? (
                                <div className="absolute -top-2 -right-2">
                                    <Button
                                        size="sm"
                                        onClick={() => onToggle(true)}
                                        className="h-5 min-w-[20px] rounded-full p-0 text-xs font-medium animate-pulse"
                                    >
                                        {newRollsCount}
                                    </Button>
                                </div>
                            ) : (
                                <div className="absolute -top-2 -right-2">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            setIsPaused(!isPaused)
                                        }}
                                        className="h-5 w-5 p-0"
                                    >
                                        {isPaused ? (
                                            <Play className="h-4 w-4" />
                                        ) : (
                                            <Pause className="h-4 w-4" />
                                        )}
                                    </Button>
                                </div>
                            )}
                        </div>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Roll History ({isPaused ? 'Paused' : 'Live'})</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>

            <Dialog
                open={isOpen}
                onOpenChange={(open) => !open && onToggle(false)}
            >
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            Recent Roll History
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setIsPaused(!isPaused)}
                                className="h-8 w-8"
                            >
                                {isPaused ? (
                                    <Play className="h-4 w-4" />
                                ) : (
                                    <Pause className="h-4 w-4" />
                                )}
                            </Button>
                        </DialogTitle>
                        <DialogDescription>
                            View recent dice rolls from all players.{' '}
                            {isPaused
                                ? 'Currently paused'
                                : 'Updates live every 2 seconds'}
                            .
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        {rolls?.map((roll) => {
                            const isChanceDie =
                                (roll.attributeValue ?? 0) +
                                    (roll.skillValue ?? 0) +
                                    roll.additionalDice <=
                                0

                            // If this is a willpower roll, find the original roll
                            const originalRoll = rolls?.find(
                                (r) => r.id === roll.originalRollId
                            )
                            const combinedResults = originalRoll
                                ? [
                                      ...JSON.parse(originalRoll.result),
                                      ...roll.results
                                  ]
                                : roll.results

                            const { outcome } = calculateRollResult(
                                roll.originalRollId
                                    ? combinedResults
                                    : roll.results,
                                isChanceDie
                            )

                            const isNew = new Date(roll.date) > lastOpenedTime

                            return (
                                <div
                                    key={roll.id}
                                    className={`border rounded-lg p-4 space-y-2 ${
                                        isNew ? 'border-primary' : 'opacity-50'
                                    } ${
                                        isOtherPlayerRoll(roll) &&
                                        'bg-indigo-100/20'
                                    }`}
                                >
                                    <div className="flex justify-between items-start">
                                        <div>
                                            <div className="font-semibold">
                                                {roll.characterName}
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                                Player:{' '}
                                                {userNames[roll.playerId]}
                                            </div>
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                            {formatDateTime(roll.date)}
                                        </div>
                                    </div>
                                    <div className="flex flex-col gap-2">
                                        <div className="flex flex-wrap gap-2">
                                            {!roll.originalRollId ? (
                                                <DiceResults
                                                    results={roll.results}
                                                    isChanceDie={isChanceDie}
                                                    size="sm"
                                                />
                                            ) : (
                                                <DiceResults
                                                    results={
                                                        rolls.find(
                                                            (r) =>
                                                                r.id ===
                                                                roll.originalRollId
                                                        )?.results ?? []
                                                    }
                                                    isChanceDie={isChanceDie}
                                                    size="sm"
                                                />
                                            )}
                                            {roll.isWillpowerReroll && (
                                                <>
                                                    <div className="w-full text-sm text-primary">
                                                        +3 Willpower dice:
                                                    </div>
                                                    <DiceResults
                                                        results={roll.results}
                                                        isChanceDie={
                                                            isChanceDie
                                                        }
                                                        size="sm"
                                                    />
                                                </>
                                            )}
                                        </div>
                                        <div
                                            className={`text-sm font-medium ${
                                                outcome === 'Failure' ||
                                                outcome === 'Critical Failure'
                                                    ? 'text-destructive'
                                                    : 'text-primary'
                                            }`}
                                        >
                                            {isChanceDie && '(Chance Die) '}
                                            {outcome}
                                        </div>
                                        <div className="text-sm">
                                            {roll.attribute && (
                                                <span>
                                                    {roll.attribute}{' '}
                                                    {roll.attributeValue &&
                                                        `(${roll.attributeValue})`}
                                                </span>
                                            )}
                                            {roll.skill && (
                                                <span>
                                                    {' '}
                                                    + {roll.skill}{' '}
                                                    {roll.skillValue &&
                                                        `(${roll.skillValue})`}
                                                </span>
                                            )}
                                            {roll.additionalDice ? (
                                                <span>
                                                    {roll.attribute
                                                        ? ' + Modifier '
                                                        : 'Custom Roll '}
                                                    ({roll.additionalDice})
                                                </span>
                                            ) : null}
                                            {roll.isWillpowerReroll && (
                                                <span className="text-primary">
                                                    {' '}
                                                    + Willpower (3)
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </DialogContent>
            </Dialog>
        </>
    )
}
