'use client';

import { createContext, useContext } from 'react';

type UserNamesContextType = Record<string, string>;

const UserNamesContext = createContext<UserNamesContextType>({});

export function UserNamesProvider({
    value,
    children
}: {
    value: UserNamesContextType;
    children: React.ReactNode;
}) {
    return (
        <UserNamesContext.Provider value={value}>
            {children}
        </UserNamesContext.Provider>
    );
}

export function useUserNames() {
    const context = useContext(UserNamesContext);
    return context ?? {};
}