import { type NextRequest } from 'next/server';
import minioClient from '~/server/storage/minioClient';
import { env } from '~/env';
import { auth } from '~/server/auth';

export async function GET(
    __: NextRequest,
    { params }: { params: Promise<{ path: string[] }> }
) {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
        return new Response('Unauthorized', { status: 401 });
    }

    try {
        const objectName = (await params).path.join('/');
        const dataStream = await minioClient.getObject(
            env.MINIO_BUCKET,
            objectName
        );

        // Convert stream to buffer
        const chunks = [];
        for await (const chunk of dataStream) {
            chunks.push(chunk);
        }
        const buffer = Buffer.concat(chunks);

        // Return the image with appropriate headers
        return new Response(buffer, {
            headers: {
                'Content-Type': 'image/jpeg',
                'Cache-Control': 'public, max-age=31536000, immutable'
            }
        });
    } catch (error) {
        console.error('Failed to fetch image from MinIO:', error);
        return new Response('Image not found', { status: 404 });
    }
}
