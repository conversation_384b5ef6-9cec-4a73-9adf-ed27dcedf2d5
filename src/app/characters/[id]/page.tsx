import { CharacterData } from '../../_components/character-data';
import { auth } from '../../../server/auth';
import { notFound, redirect } from 'next/navigation';
import { api } from '../../../trpc/server';

interface CharacterPageProps {
    params: Promise<{ id: string }>;
}

export default async function CharacterPage({ params }: CharacterPageProps) {
    const { id } = await params;
    const session = await auth();

    if (!session?.user) {
        redirect('/api/auth/signin');
    }

    const character = await api.character.getById({ id: parseInt(id) });

    if (!character) {
        return notFound();
    }

    let characterData;
    try {
        characterData = {
            ...JSON.parse(character.content),
            id: character.id,
            imageUrl: character.imageUrl
        };
    } catch (error) {
        throw new Error(
            'Failed to load character data. The character file might be corrupted.'
        );
    }
    return (
        <CharacterData
            savedCharacter={characterData}
            createdById={character.createdById}
        />
    );
}
