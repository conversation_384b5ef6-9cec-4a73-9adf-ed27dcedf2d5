import { Suspense } from 'react';
import { CreateCharacterDialog } from '../_components/create-character-dialog';
import { Button } from '../_components/ui/button';
import { PlusIcon } from 'lucide-react';
import { Header } from '../_components/layout/header';
import { Loading } from '../_components/ui/loading';
import { api } from '~/trpc/server';
import { CharacterList } from '../_components/character-list';

async function CharacterListContainer() {
    const characters = await api.character.getList();
    return <CharacterList initialCharacters={characters} />;
}

export default function CharacterListPage() {
    return (
        <div className="min-h-screen flex flex-col">
            <Header backHref="/" backText="Back">
                <CreateCharacterDialog>
                    <Button>
                        <PlusIcon className="w-4 h-4 mr-2" />
                        New Character
                    </Button>
                </CreateCharacterDialog>
            </Header>
            <main className="flex-1 px-2 py-8 relative flex items-center flex-col">
                <Suspense fallback={<Loading />}>
                    <CharacterListContainer />
                </Suspense>
            </main>
        </div>
    );
}


