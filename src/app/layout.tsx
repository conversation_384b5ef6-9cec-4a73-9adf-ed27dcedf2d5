import '~/styles/globals.css';

import type { Metadata, Viewport } from 'next';
import { <PERSON>ei<PERSON> } from 'next/font/google';
import { SessionProvider } from 'next-auth/react';
import { auth } from '~/server/auth';

import { TRPCReactProvider } from '~/trpc/react';
import { api } from '../trpc/server';
import { UserNamesProvider } from './_contexts/user-names-context';

export const metadata: Metadata = {
    title: 'Character Sheet',
    description: 'Interactive CoD-style character sheet',
    icons: [{ rel: 'icon', url: '/favicon.ico' }]
};

export const viewport: Viewport = {
    initialScale: 1,
    width: 'device-width',
    viewportFit: 'cover'
};

const geist = Geist({
    subsets: ['latin'],
    variable: '--font-geist-sans'
});

export default async function RootLayout({
    children
}: Readonly<{ children: React.ReactNode }>) {
    const session = await auth();
    const users = session?.user.isAdmin ? await api.user.getList() : [];
    const userNames = users.reduce<Record<string, string>>((acc, user) => {
        acc[user.id] = user.name ?? 'Unknown';
        return acc;
    }, {});

    return (
        <html lang="en" className={`${geist.variable}`}>
            <body>
                <SessionProvider session={session}>
                    <TRPCReactProvider>
                        <UserNamesProvider value={userNames}>
                            {children}
                        </UserNamesProvider>
                    </TRPCReactProvider>
                </SessionProvider>
            </body>
        </html>
    );
}
