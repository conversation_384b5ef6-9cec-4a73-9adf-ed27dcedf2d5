"use client";

import { useCallback, useEffect } from 'react'

const DEFAULT_DELAY = 500

export function useDebouncedCallback<T>(
    callback: ((value: T) => void) | undefined,
    delay = DEFAULT_DELAY
) {
    let timeoutId: NodeJS.Timeout
    useEffect(() => clearTimeout(timeoutId), [callback, delay])

    return useCallback(
        (() => {
            return (value: T) => {
                clearTimeout(timeoutId)
                timeoutId = setTimeout(() => {
                    callback?.(value)
                }, delay)
            }
        })(),
        [callback, delay]
    )
}