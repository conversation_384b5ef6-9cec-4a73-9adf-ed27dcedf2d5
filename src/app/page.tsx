import Link from 'next/link';
import { auth } from '~/server/auth';
import { HydrateClient } from '~/trpc/server';
import { Header } from './_components/layout/header';
import { Card, CardContent } from './_components/ui/card';
import { useIsAdmin } from '../lib/auth'

export default async function Home() {
    const session = await auth();

    const isAdmin = session?.user.isAdmin;

    return (
        <HydrateClient>
            <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-1 px-2 py-8 relative flex items-center justify-center">
                    <div className="container">
                        <div className="mx-auto max-w-3xl">
                            <Card>
                                <CardContent className="p-6 flex flex-col items-center gap-6">
                                    <h1 className="text-2xl font-bold">
                                        Character Sheet App
                                    </h1>

                                    <div className="flex flex-col items-center gap-4">
                                        {session && (
                                            <p className="text-lg text-muted-foreground">
                                                Welcome, {session.user?.name}{' '}
                                                {isAdmin ? '(Admin)' : ''}
                                            </p>
                                        )}

                                        <div className="flex flex-col gap-3 w-full max-w-xs">
                                            {!!session && (
                                                <Link
                                                    href="/characters"
                                                    className="w-full rounded-lg bg-secondary px-6 py-2 text-center font-semibold text-secondary-foreground transition-colors hover:bg-secondary/90"
                                                >
                                                    My Characters
                                                </Link>
                                            )}
                                            <Link
                                                href={
                                                    session
                                                        ? '/api/auth/signout'
                                                        : '/api/auth/signin'
                                                }
                                                className="w-full rounded-lg bg-primary px-6 py-2 text-center font-semibold text-primary-foreground transition-colors hover:bg-primary/90"
                                            >
                                                {session
                                                    ? 'Sign out'
                                                    : 'Sign in'}
                                            </Link>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </main>
            </div>
        </HydrateClient>
    );
}
