interface RollCalculation {
    successCount: number;
    outcome: string;
}

export function calculateRollResult(results: number[], isChanceDie: boolean): RollCalculation {
    // Count successes based on roll type
    const successCount = results.filter((result) =>
        isChanceDie ? result === 10 : result >= 8
    ).length;

    // Determine roll outcome
    let outcome = '';
    if (isChanceDie) {
        if (results.every((result) => result === 1)) {
            outcome = 'Critical Failure';
        } else {
            outcome = successCount > 0 ? 'Success' : 'Failure';
        }
    } else {
        switch (successCount) {
            case 0:
                outcome = 'Failure';
                break;
            case 1:
            case 2:
                outcome = 'Partial Success';
                break;
            case 3:
            case 4:
                outcome = 'Clean Success';
                break;
            default:
                outcome = 'Critical Success';
        }
    }

    return { successCount, outcome };
}