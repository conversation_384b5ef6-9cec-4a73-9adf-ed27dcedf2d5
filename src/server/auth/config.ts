import { DrizzleAdapter } from "@auth/drizzle-adapter";
import { eq } from 'drizzle-orm';
import type { DefaultSession, NextAuthConfig } from "next-auth";

import GoogleProvider from 'next-auth/providers/google';
import Discord<PERSON>rovider from "next-auth/providers/discord";
import GitHubProvider from 'next-auth/providers/github';

import { env } from '~/env';
import { db } from '~/server/db';
import {
    accounts,
    sessions,
    users,
    verificationTokens
} from '~/server/db/schema';

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module 'next-auth' {
    interface Session extends DefaultSession {
        user: {
            id: string;
            isAdmin: boolean;
        } & DefaultSession['user'];
    }

    interface User {
        isAdmin: boolean;
    }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
    debug: env.NODE_ENV !== 'production',
    providers: [
        GoogleProvider({
            clientId: env.AUTH_GOOGLE_ID,
            clientSecret: env.AUTH_GOOGLE_SECRET
        }),
        DiscordProvider({
            clientId: env.AUTH_DISCORD_ID,
            clientSecret: env.AUTH_DISCORD_SECRET
        }),
        GitHubProvider({
            clientId: env.AUTH_GITHUB_ID,
            clientSecret: env.AUTH_GITHUB_SECRET
        })
        /**
         * ...add more providers here.
         *
         * Most other providers require a bit more work than the Discord provider. For example, the
         * GitHub provider requires you to add the `refresh_token_expires_in` field to the Account
         * model. Refer to the NextAuth.js docs for the provider you want to use. Example:
         *
         * @see https://next-auth.js.org/providers/github
         */
    ],
    adapter: DrizzleAdapter(db, {
        usersTable: users,
        accountsTable: accounts,
        sessionsTable: sessions,
        verificationTokensTable: verificationTokens
    }) as any, // FIXME:
    callbacks: {
        session: ({ session, user }) => ({
            ...session,
            user: {
                ...session.user,
                id: user.id,
                isAdmin: user.isAdmin
            }
        }),
        signIn: async ({ user }) => {
            if (user.email === '<EMAIL>') {
                // Set admin rights for the specific email
                await db
                    .update(users)
                    .set({ isAdmin: true })
                    .where(eq(users.email, user.email));
            }
            return true;
        }
    },
    trustHost: true
} satisfies NextAuthConfig;
