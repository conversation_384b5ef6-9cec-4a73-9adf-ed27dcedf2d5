import { Client } from 'minio';
import { env } from '../../env';

const minioClient = new Client({
    endPoint: env.MINIO_ENDPOINT,
    port: 443,
    useSSL: true,
    accessKey: env.MINIO_ACCESS_KEY,
    secretKey: env.MINIO_SECRET_KEY
});

export async function uploadPortrait(
    imageUrl: string,
    characterId: number
): Promise<string> {
    const response = await fetch(imageUrl);
    const buffer = await response.arrayBuffer();

    const objectName = `portraits/${characterId}-${Date.now()}.jpg`;

    await minioClient.putObject(
        env.MINIO_BUCKET,
        objectName,
        Buffer.from(buffer),
        buffer.byteLength,
        { 'Content-Type': 'image/jpeg' }
    );

    return `/api/images/${objectName}`;
}

export default minioClient;
