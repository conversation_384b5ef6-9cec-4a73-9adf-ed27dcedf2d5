import { Civitai, Scheduler } from "civitai";
import { setTimeout } from 'node:timers/promises'
import { env } from '../../env'


export const enhanceImageGenerationPrompt = (imagePrompt: string) => {
    return `graphic novel illustration, gritty cyberpunk aesthetic, digital portrait, half-body portrait, ${imagePrompt}, visible advanced technology, high contrast, dramatic lighting`
}

export async function generateCharacterPortrait(imagePrompt: string) {
    const civitai = new Civitai({
        auth: env.CIVITAI_API_KEY
    })

    console.log('Generating portrait...', env.CIVITAI_MODEL_ID)
    const response = await civitai.image.fromText({
        model: env.CIVITAI_MODEL_ID,
        params: {
            prompt: imagePrompt,
            negativePrompt:
                '(worst quality, low quality:1.4), photo, realistic, anime, blurry, text, watermark, lowres, boring, nsfw, porn, signature, photography, 3d render, cg, bad hands, nude, (flag: 1.7), (logo), fantasy, medieval, steampunk, modern day, simple background',
            scheduler: Scheduler.DPM2KARRAS,
            steps: 30,
            cfgScale: 4,
            width: 800,
            height: 800
        }
    })

    // wait first
    await setTimeout(20_000)

    let retriesLeft = 30
    const retryInterval = 10_000
    while (retriesLeft-- > 0) {
        const output = await civitai.jobs.getByToken(response.token)
        const result = output?.jobs?.at(0)?.result.at(0)
        if (result.available) {
            console.log('Portrait generated!')
            return result.blobUrl
        }
        console.log(result, 'Will retry in 10 seconds...', { retriesLeft })
        await setTimeout(retryInterval)
    }
};
