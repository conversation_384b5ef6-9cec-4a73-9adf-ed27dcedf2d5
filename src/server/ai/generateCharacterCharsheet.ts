import {
    GoogleGenerativeAI,
    type EnhancedGenerateContentResponse
} from '@google/generative-ai';
import { env } from '~/env'
import { z } from 'zod'

const AttributeSchema = z.number().int().min(1).max(5)
const SkillSchema = z.number().int().min(0).max(4)

const IconEnum = z.enum([
    'Dancer-on-the-Wires',
    'The Smiling Beast',
    'The Silent Watcher',
    'The Fractured One',
    'Sister-behind-the-Veil',
    'Hunger-in-the-Gloom',
    'Ashen Mother',
    'He-Who-Charts-the-Paths',
    'Nobody'
])

const MeritSchema = z.object({
    name: z.string().min(1),
    value: z.number().int().min(0).max(5),
    description: z.string()
})

const InventoryItemSchema = z.object({
    name: z.string().min(1),
    description: z.string(),
    modifiers: z.string() // Assuming simple string for modifiers, allow empty
})

// Define skill specializations: Record where keys are skill names (strings)
// and values are arrays containing exactly one string specialization. Optional.
const SkillSpecializationsSchema = z
    .record(
        z.string(), // Key: Skill name
        z.array(z.string().min(1)).length(1) // Value: Array with exactly one non-empty string
    )
    .optional() // Optional: Character might have no specializations

const GMNotesSchema = z.object({
    appearance: z.string().min(1),
    short_bio: z.string().min(1),
    personality: z.string().min(1),
    quirks: z.array(z.string().min(1)).min(1).max(2), // Expect 1-2 quirks
    imageGenerationPrompt: z.string(), // Allow any string, no min length enforced by default
    faction: z.string().min(1)
})

// Main Character Sheet Schema
const CharacterSheetSchema = z.object({
    name: z.string().min(1),
    concept: z.string().min(1),
    icon: IconEnum,
    virtue: z.string().min(1),
    vice: z.string().min(1),
    health: z.object({
        // Using number(), but could add .default(10) if needed for generation logic
        filled: z.number().int().min(0),
        damaged: z.number().int().min(0) // .default(0)
    }),
    willpower: z.object({
        filled: z.number().int().min(0), // .default(10)
        damaged: z.number().int().min(0) // .default(0)
    }),
    attributes: z.object({
        Intelligence: AttributeSchema,
        Wits: AttributeSchema,
        Resolve: AttributeSchema,
        Strength: AttributeSchema,
        Dexterity: AttributeSchema,
        Stamina: AttributeSchema,
        Presence: AttributeSchema,
        Manipulation: AttributeSchema,
        Composure: AttributeSchema
    }),
    // Note: Complex rules like "6-8 dots per category" require .refine() or external validation logic.
    // This schema primarily validates types and individual ranges.
    skills: z.object({
        // Updated Skill List
        Academics: SkillSchema,
        Computer: SkillSchema,
        Engineering: SkillSchema, // Added
        Investigation: SkillSchema,
        Medicine: SkillSchema,
        Occult: SkillSchema, // Added
        Science: SkillSchema,
        Athletics: SkillSchema,
        Firearms: SkillSchema, // Added (replaces Brawl?)
        Melee: SkillSchema, // Added
        Piloting: SkillSchema, // Added (replaces Pilot?)
        Security: SkillSchema, // Added
        Stealth: SkillSchema,
        Survival: SkillSchema, // Added
        Empathy: SkillSchema, // Added (replaces Animal Ken?)
        Expression: SkillSchema,
        Intimidation: SkillSchema,
        Persuasion: SkillSchema,
        Socialize: SkillSchema,
        Streetwise: SkillSchema,
        Subterfuge: SkillSchema
        // Removed: Crafts, Politics, Religion, Brawl, Pilot, Animal Ken
    }),
    merits: z.array(MeritSchema).min(1).max(3), // Expect 1-3 merits based on guidelines
    inventory: z.array(InventoryItemSchema), // Allow empty inventory (0 items)
    ambitions: z.array(z.string().min(1)).min(1).max(3), // Expect 1-3 ambitions based on guidelines
    skillSpecializations: SkillSpecializationsSchema,
    gmNotes: GMNotesSchema
})

// Define the type based on the schema
type CharacterSheet = z.infer<typeof CharacterSheetSchema>

// Define custom error types
class LLMGenerationError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'LLMGenerationError'
    }
}
class JsonExtractionError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'JsonExtractionError'
    }
}
class InvalidSchemaError extends Error {
    public issues: z.ZodIssue[]
    constructor(message: string, issues: z.ZodIssue[]) {
        super(message)
        this.name = 'InvalidSchemaError'
        this.issues = issues
    }
}

// --- LLM Setup (Consider making model name configurable) ---
const genAI = new GoogleGenerativeAI(env.GEMINI_API_KEY)
const modelName = 'gemini-2.0-flash' // Or from config

const model = genAI.getGenerativeModel({
    model: modelName,
    generationConfig: {
        responseMimeType: 'application/json',
        temperature: 0.7, // Adjust as needed
        maxOutputTokens: 8192 // Ensure sufficient size
    }
})

// --- Helper function to extract JSON ---
function extractJson(text: string): string | undefined {
    // Simple regex to find JSON block, might need refinement
    const match = text.match(/```(?:json)?\s*([\s\S]*?)\s*```|({[\s\S]*})/)
    if (match) {
        // Prefer the explicitly fenced block (group 1), fallback to raw object (group 2)
        return match[1] || match[2]
    }
    // If no clear block found, maybe the whole string is JSON? Try it as is.
    if (text.trim().startsWith('{') && text.trim().endsWith('}')) {
        return text.trim()
    }
    return undefined // JSON not found
}

export const generateNotesFromGmNotes = (gmNotes?: {
    appearance: string
    short_bio: string
    personality: string
    quirks: string[]
    imageGenerationPrompt: string
    faction: string
}) => {
    return [
        {
            title: 'Appearance',
            content: gmNotes?.appearance ?? ''
        },
        {
            title: 'Short Bio',
            content: gmNotes?.short_bio ?? ''
        },
        {
            title: 'Personality',
            content: gmNotes?.personality ?? ''
        },
        {
            title: 'Quirks',
            content: gmNotes?.quirks?.join('\n') ?? ''
        },
        {
            title: 'Faction',
            content: gmNotes?.faction ?? ''
        }
    ]
}

// --- Main Generation Function ---
export async function generateCharacterCharsheet(
    description: string,
    context: string
): Promise<Awaited<ReturnType<typeof CharacterSheetSchema.safeParse>>['data']> {
    const prompt = `${context}

Generate a character based on this description: "${description}"

Respond ONLY with the JSON object, no additional text or explanations. Make sure it strictly follows the provided JSON structure definition.`

    let response: EnhancedGenerateContentResponse | undefined
    let rawText: string | null = null

    try {
        const result = await model.generateContent(prompt)
        response = result.response

        if (!response) {
            console.error(
                `No response object received from the API for model ${modelName}.`
            )
            throw new LLMGenerationError(
                `No response object received from the language model (${modelName}).`
            )
        }

        // Log candidate info regardless of success/failure for debugging
        const candidate = response.candidates?.[0]
        if (candidate) {
            console.log(
                `LLM Candidate Info (${modelName}): Finish Reason = ${
                    candidate.finishReason
                }, Safety Ratings = ${JSON.stringify(candidate.safetyRatings)}`
            )
        } else {
            console.log(
                `LLM Response (${modelName}) received, but contained no candidates.`
            )
        }

        // Check for explicit blocking first
        if (
            candidate?.finishReason &&
            ['SAFETY', 'RECITATION', 'OTHER'].includes(candidate.finishReason)
        ) {
            console.error(
                `LLM generation potentially blocked (${modelName}). Reason: ${candidate.finishReason}`
            )
            throw new LLMGenerationError(
                `LLM generation blocked. Reason: ${candidate.finishReason}`
            )
        }

        try {
            rawText = response.text()
        } catch (textError) {
            console.error(
                `Error extracting text from LLM response (${modelName}):`,
                textError
            )
            // Rethrow or handle as an LLM error, possibly check finishReason again
            throw new LLMGenerationError(
                `Failed to extract text content from LLM response (${modelName}). Reason: ${
                    candidate?.finishReason ?? 'Unknown'
                }`
            )
        }

        if (!rawText?.trim()) {
            console.error(
                `Received an empty text response from LLM (${modelName}). Potentially blocked or empty generation.`
            )
            throw new LLMGenerationError(
                `Received empty text response from LLM (${modelName}) (Finish Reason: ${
                    candidate?.finishReason ?? 'N/A'
                }).`
            )
        }

        // --- 2. Extract JSON reliably ---
        const jsonString = extractJson(rawText)
        if (!jsonString) {
            console.error(
                `Could not extract valid JSON block from LLM response (${modelName}). Raw text:\n${rawText}`
            )
            throw new JsonExtractionError(
                `Failed to extract JSON block from LLM response (${modelName}).`
            )
        }

        // --- 3. Parse and Validate JSON ---
        let parsedJson: unknown
        try {
            parsedJson = JSON.parse(jsonString)
        } catch (parseError) {
            console.error(
                `Failed to parse extracted JSON string (${modelName}):`,
                parseError
            )
            console.error('Extracted JSON string:', jsonString)
            throw new JsonExtractionError(
                `Invalid JSON syntax in LLM response (${modelName}).`
            )
        }

        const validationResult = CharacterSheetSchema.safeParse(parsedJson)

        if (!validationResult.success) {
            console.error(
                `LLM response failed schema validation (${modelName}):`,
                validationResult.error.issues
            )
            throw new InvalidSchemaError(
                `Generated character sheet failed validation (${modelName}).`,
                validationResult.error.issues
            )
        }

        const validCharsheet = validationResult.data

        return validCharsheet
    } catch (e) {
        // Log specific error types differently if needed
        if (e instanceof InvalidSchemaError) {
            // Already logged issues, just rethrow or handle
            console.error(`Schema validation failed for ${modelName}.`)
            // Optional: Log raw text if available and useful
            // if (rawText) console.error("Raw text received:", rawText);
        } else if (e instanceof JsonExtractionError) {
            console.error(`JSON extraction/parsing error for ${modelName}.`)
            // Optional: Log raw text if available and useful
            // if (rawText) console.error("Raw text received:", rawText);
        } else if (e instanceof LLMGenerationError) {
            console.error(`LLM generation error for ${modelName}.`)
        } else if (e instanceof Error) {
            console.error(
                `Unexpected error during character generation (${modelName}):`,
                e.message,
                e.stack
            )
        } else {
            console.error(
                `Unknown error during character generation (${modelName}):`,
                e
            )
        }

        // Rethrow the original error or a new standardized error
        throw e // Or wrap in a generic error if preferred: throw new Error(`Character generation failed: ${e.message}`);
    }
}
