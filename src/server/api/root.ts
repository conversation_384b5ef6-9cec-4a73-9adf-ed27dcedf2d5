import { characterRouter } from '~/server/api/routers/charsheet';
import { createCallerFactory, createTRPCRouter } from '~/server/api/trpc';
import { userRouter } from '~/server/api/routers/user';
import { notesRouter } from '~/server/api/routers/notes';
import { loreRouter } from '~/server/api/routers/lore';
import { diceRollRouter } from '~/server/api/routers/diceRolls';
import { gmNotesRouter } from '~/server/api/routers/gmNotes';

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
    character: characterRouter,
    user: userRouter,
    notes: notesRouter,
    lore: loreRouter,
    diceRolls: diceRollRouter,
    gmNotes: gmNotesRouter
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
