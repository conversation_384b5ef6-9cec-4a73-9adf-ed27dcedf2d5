import { z } from 'zod'
import { and, eq } from 'drizzle-orm'
import {
    adminProcedure,
    createTRPCRouter,
    protectedProcedure
} from '~/server/api/trpc'
import { characters, playerNotes, gmNotes } from '~/server/db/schema'
import {
    generateCharacterCharsheet,
    generateNotesFromGmNotes
} from '~/server/ai/generateCharacterCharsheet'
import { commonContext } from '~/server/ai/commonPrompt'
import {
    enhanceImageGenerationPrompt,
    generateCharacterPortrait
} from '~/server/ai/generateCharacterPortrait'
import minioClient, { uploadPortrait } from '~/server/storage/minioClient'
import { TRPCError } from '@trpc/server'
import { env } from '../../../env'
import type { User } from 'next-auth'
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js'
import type postgres from 'postgres'

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB in bytes
const ALLOWED_MIME_TYPES = ['image/jpeg', 'image/png', 'image/webp']

export const characterRouter = createTRPCRouter({
    create: protectedProcedure
        .input(z.object({ content: z.string().min(1) }))
        .mutation(async ({ ctx, input }) => {
            const parsedContent = JSON.parse(input.content)
            return createCharacterFromSource(ctx, parsedContent)
        }),

    update: protectedProcedure
        .input(z.object({ id: z.number(), content: z.string().min(1) }))
        .mutation(async ({ ctx, input }) => {
            const updateOp = ctx.db
                .update(characters)
                .set({ content: input.content })

            const op = ctx.session.user.isAdmin
                ? updateOp.where(eq(characters.id, input.id))
                : updateOp.where(
                      and(
                          eq(characters.id, input.id),
                          eq(characters.createdById, ctx.session.user.id)
                      )
                  )
            await op
        }),

    getList: protectedProcedure.query(async ({ ctx }) => {
        const characters = await ctx.db.query.characters.findMany({
            where: ctx.session.user.isAdmin
                ? undefined
                : (fields, operators) =>
                      operators.eq(fields.createdById, ctx.session.user.id)
        })

        return characters ?? null
    }),

    getById: protectedProcedure
        .input(z.object({ id: z.number() }))
        .query(async ({ ctx, input }) => {
            const result = await ctx.db.query.characters.findFirst({
                where: ctx.session.user.isAdmin
                    ? eq(characters.id, input.id)
                    : and(
                          eq(characters.id, input.id),
                          eq(characters.createdById, ctx.session.user.id)
                      )
            })
            return result
        }),

    delete: protectedProcedure
        .input(z.object({ id: z.number() }))
        .mutation(async ({ ctx, input }) => {
            const deleteOp = ctx.db.delete(characters)
            await (ctx.session.user.isAdmin
                ? deleteOp.where(eq(characters.id, input.id))
                : deleteOp.where(
                      and(
                          eq(characters.id, input.id),
                          eq(characters.createdById, ctx.session.user.id)
                      )
                  ))
        }),

    generateFromDescription: adminProcedure
        .input(z.object({ description: z.string().min(1) }))
        .mutation(async ({ ctx, input }) => {
            try {
                const generatedJson = await generateCharacterCharsheet(
                    input.description,
                    commonContext
                )

                return createCharacterFromSource(ctx, generatedJson)
            } catch (error) {
                throw new TRPCError({
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'Failed to generate character',
                    cause: error
                })
            }
        }),

    getUploadUrl: protectedProcedure
        .input(
            z.object({
                characterId: z.number(),
                contentType: z
                    .string()
                    .refine(
                        (type) => ALLOWED_MIME_TYPES.includes(type),
                        'Invalid file type. Only JPEG, PNG and WebP images are allowed.'
                    ),
                fileSize: z
                    .number()
                    .max(MAX_FILE_SIZE, 'File size must be under 5MB')
            })
        )
        .mutation(async ({ ctx, input }) => {
            // Verify user owns the character or is admin
            const character = await ctx.db.query.characters.findFirst({
                where: ctx.session.user.isAdmin
                    ? eq(characters.id, input.characterId)
                    : and(
                          eq(characters.id, input.characterId),
                          eq(characters.createdById, ctx.session.user.id)
                      )
            })

            if (!character) {
                throw new TRPCError({
                    code: 'NOT_FOUND',
                    message: 'Character not found'
                })
            }

            const objectName = `portraits/${
                input.characterId
            }-${Date.now()}.jpg`
            const url = await minioClient.presignedPutObject(
                env.MINIO_BUCKET,
                objectName,
                60
            )

            return {
                uploadUrl: url,
                imageUrl: `/api/images/${objectName}`
            }
        }),

    regeneratePortrait: adminProcedure
        .input(
            z.object({
                characterId: z.number(),
                prompt: z.string()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const character = await ctx.db.query.characters.findFirst({
                where: eq(characters.id, input.characterId)
            })

            if (!character) {
                throw new TRPCError({
                    code: 'NOT_FOUND',
                    message: 'Character not found'
                })
            }

            try {
                const imageGenerationPrompt = input.prompt

                if (!imageGenerationPrompt) {
                    throw new TRPCError({
                        code: 'BAD_REQUEST',
                        message: 'No image generation prompt found'
                    })
                }

                await ctx.db
                    .update(gmNotes)
                    .set({ imageGenerationPrompt: input.prompt })
                    .where(eq(gmNotes.characterId, input.characterId))

                const tempImageUrl = await generateCharacterPortrait(
                    imageGenerationPrompt
                )

                if (tempImageUrl) {
                    try {
                        const permanentUrl = await uploadPortrait(
                            tempImageUrl,
                            character.id
                        )

                        await ctx.db
                            .update(characters)
                            .set({ imageUrl: permanentUrl })
                            .where(eq(characters.id, character.id))

                        return permanentUrl
                    } catch (err) {
                        console.error(
                            'Failed to upload portrait to MinIO:',
                            err
                        )
                        throw new TRPCError({
                            code: 'INTERNAL_SERVER_ERROR',
                            message: 'Failed to upload portrait to MinIO'
                        })
                    }
                }

                return null
            } catch (error) {
                if (TRPCError instanceof TRPCError) {
                    throw error
                }
                console.error(
                    'Failed to regenerate portrait:',
                    error,
                    (error as any)?.body
                )
                throw new TRPCError({
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'Failed to regenerate portrait'
                })
            }
        }),

    updateImageUrl: protectedProcedure
        .input(
            z.object({
                id: z.number(),
                imageUrl: z.string()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const updateOp = ctx.db
                .update(characters)
                .set({ imageUrl: input.imageUrl })

            await (ctx.session.user.isAdmin
                ? updateOp.where(eq(characters.id, input.id))
                : updateOp.where(
                      and(
                          eq(characters.id, input.id),
                          eq(characters.createdById, ctx.session.user.id)
                      )
                  ))
        }),

    getImagePrompt: adminProcedure
        .input(z.object({ characterId: z.number() }))
        .query(async ({ ctx, input }) => {
            const gmNote = await ctx.db.query.gmNotes.findFirst({
                where: eq(gmNotes.characterId, input.characterId)
            })

            return gmNote?.imageGenerationPrompt ?? ''
        })
})

const createCharacterFromSource = async (
    ctx: {
        session: {
            user: {
                id: string
                isAdmin: boolean
            } & User
            expires: string
        }
        db: PostgresJsDatabase<any> & {
            $client: postgres.Sql<{}>
        }
    },
    generatedJson: Awaited<ReturnType<typeof generateCharacterCharsheet>>
) => {
    generatedJson?.gmNotes
    const { gmNotes: gmNotesData, ...charsheetWithoutGmNotes } =
        generatedJson ?? {
            gmNotes: undefined
        }

    const [character] = await ctx.db
        .insert(characters)
        .values({
            content: JSON.stringify(charsheetWithoutGmNotes),
            createdById: ctx.session.user.id
        })
        .returning()
        .execute()
    if (character && gmNotesData) {
        // Insert GM notes into the new table
        const imageGenerationPrompt = enhanceImageGenerationPrompt(
            gmNotesData.imageGenerationPrompt
        )
        await ctx.db
            .insert(gmNotes)
            .values({
                characterId: character.id,
                appearance: gmNotesData.appearance,
                shortBio: gmNotesData.short_bio,
                personality: gmNotesData.personality,
                quirks: Array.isArray(gmNotesData.quirks)
                    ? gmNotesData.quirks.join('\n')
                    : gmNotesData.quirks,
                imageGenerationPrompt,
                faction: gmNotesData.faction,
                updatedById: ctx.session.user.id
            })
            .execute()

        // Insert player notes
        const notes = generateNotesFromGmNotes(gmNotesData)
        await ctx.db
            .insert(playerNotes)
            .values(
                notes.map((note) => ({
                    title: note.title,
                    content: note.content,
                    characterId: character.id,
                    createdById: ctx.session.user.id
                }))
            )
            .execute()

        // Fire and forget, but with error handling and ensuring DB update completes
        if (imageGenerationPrompt) {
            void generateCharacterPortrait(imageGenerationPrompt)
                .then(async (tempImageUrl) => {
                    if (tempImageUrl) {
                        try {
                            const permanentUrl = await uploadPortrait(
                                tempImageUrl,
                                character.id
                            )
                            await ctx.db
                                .update(characters)
                                .set({ imageUrl: permanentUrl })
                                .where(eq(characters.id, character.id))
                            console.log(
                                'Portrait generated and saved to MinIO',
                                {
                                    permanentUrl
                                }
                            )
                        } catch (error) {
                            console.error(
                                'Failed to upload portrait to MinIO:',
                                error
                            )
                        }
                    }
                })
                .catch((error) => {
                    console.error('Failed to generate portrait:', error)
                    console.error(
                        'Failed to generate portrait:',
                        (error as any)?.body
                    )
                })
        }
    }

    return character
}
