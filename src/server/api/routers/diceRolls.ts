import { z } from 'zod'
import {
    adminProcedure,
    createTRPCRouter,
    protectedProcedure
} from '~/server/api/trpc'
import { diceRolls, characters } from '~/server/db/schema'
import { eq } from 'drizzle-orm'

export const diceRollRouter = createTRPCRouter({
    performDiceRoll: protectedProcedure
        .input(
            z.object({
                characterId: z.number(),
                diceCount: z.number(),
                attribute: z.string().optional(),
                attributeValue: z.number(),
                skill: z.string().optional(),
                skillValue: z.number(),
                additionalDice: z.number(),
                isWillpowerReroll: z.boolean(),
                originalRollId: z.number().optional(),
                specialization: z.string().optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            let pendingRolls =
                input.diceCount <= 0
                    ? 1
                    : input.isWillpowerReroll
                    ? 3
                    : input.diceCount

            const results = makeRoll(pendingRolls, input.diceCount <= 0)

            const roll = await ctx.db
                .insert(diceRolls)
                .values({
                    playerId: ctx.session.user.id,
                    characterId: input.characterId,
                    attribute: input.attribute,
                    attributeValue: input.attributeValue,
                    skill: input.skill,
                    skillValue: input.skillValue,
                    additionalDice: input.additionalDice,
                    result: JSON.stringify(results),
                    isWillpowerReroll: input.isWillpowerReroll,
                    originalRollId: input.originalRollId,
                    specialization: input.specialization
                })
                .returning()
                .execute()

            return { results, rollId: roll.at(0)!.id }
        }),

    getRecentRolls: adminProcedure.query(async ({ ctx }) => {
        const rolls = await ctx.db.query.diceRolls.findMany({
            limit: 50,
            orderBy: (diceRolls, { desc }) => [desc(diceRolls.date)],
            with: {
                character: {
                    columns: {
                        content: true
                    }
                }
            }
        })

        return rolls.map((roll) => ({
            ...roll,
            results: JSON.parse(roll.result),
            characterName:
                JSON.parse(roll.character.content).name || 'Unnamed Character'
        }))
    }),

    performGroupDiceRoll: adminProcedure
        .input(
            z.object({
                characterIds: z.array(z.number()),
                attribute: z.string().optional(),
                skill: z.string().optional(),
                additionalDice: z.number(),
                isDualAttributeMode: z.boolean().optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const results = []

            for (const characterId of input.characterIds) {
                const character = await ctx.db.query.characters.findFirst({
                    where: eq(characters.id, characterId)
                })

                if (!character) continue

                const characterData = JSON.parse(character.content)
                const attributeValue = input.attribute
                    ? characterData.attributes[input.attribute]
                    : 0

                let skillValue = 0

                // Handle dual attribute mode
                if (input.isDualAttributeMode && input.skill) {
                    // Use the second attribute value instead of a skill
                    skillValue = characterData.attributes[input.skill] || 0
                } else if (input.skill) {
                    // Normal mode: use skill value
                    skillValue = characterData.skills[input.skill] || 0
                }

                const diceCount =
                    attributeValue + skillValue + input.additionalDice
                const isChanceDie = diceCount <= 0
                const actualDiceCount = isChanceDie ? 1 : diceCount

                const diceResults = makeRoll(actualDiceCount, isChanceDie)

                const roll = await ctx.db
                    .insert(diceRolls)
                    .values({
                        playerId: ctx.session.user.id,
                        characterId,
                        attribute: input.attribute,
                        attributeValue,
                        skill: input.skill,
                        skillValue,
                        additionalDice: input.additionalDice,
                        result: JSON.stringify(diceResults),
                        isWillpowerReroll: false
                    })
                    .returning()
                    .execute()

                results.push({
                    characterId,
                    characterName: characterData.name,
                    results: diceResults,
                    rollId: roll.at(0)!.id,
                    isChanceDie,
                    attribute: input.attribute,
                    skill: input.skill,
                    attributeValue,
                    skillValue
                })
            }

            return results
        })
})

const makeRoll = (pendingRolls: number, isChanceDie: boolean) => {
    let results: number[] = []
    const rollDie = () => Math.floor(Math.random() * 10) + 1
    while (pendingRolls > 0) {
        const roll = rollDie()
        results.push(roll)
        pendingRolls--

        // Add an extra die for each 10
        if (roll === 10 && !isChanceDie) {
            pendingRolls++
        }
    }
    return results
}
