import { z } from 'zod';
import { adminProcedure, createTR<PERSON><PERSON>outer, protectedProcedure } from '~/server/api/trpc';
import { characterShares, loreItems } from '~/server/db/schema';
import { eq } from 'drizzle-orm';

export const loreRouter = createTRPCRouter({
    getLoreItems: protectedProcedure.query(async ({ ctx }) => {
        return ctx.db.query.loreItems.findMany({
            orderBy: (items) => [items.createdAt]
        })
    }),

    createLoreItem: adminProcedure
        .input(
            z.object({
                title: z.string(),
                content: z.string()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const [item] = await ctx.db
                .insert(loreItems)
                .values({
                    title: input.title,
                    content: input.content,
                    createdById: ctx.session.user.id
                })
                .returning()
            return item
        }),

    updateLoreItem: adminProcedure
        .input(
            z.object({
                id: z.number(),
                title: z.string(),
                content: z.string()
            })
        )
        .mutation(async ({ ctx, input }) => {
            await ctx.db
                .update(loreItems)
                .set({
                    title: input.title,
                    content: input.content
                })
                .where(eq(loreItems.id, input.id))
        }),

    deleteLoreItem: protectedProcedure
        .input(z.object({ id: z.number() }))
        .mutation(async ({ ctx, input }) => {
            await ctx.db.delete(loreItems).where(eq(loreItems.id, input.id))
        }),

    shareCharacter: adminProcedure
        .input(
            z.object({
                characterId: z.number(),
                userSettings: z.array(
                    z.object({
                        userId: z.string(),
                        customConcept: z.string().optional()
                    })
                )
            })
        )
        .mutation(async ({ ctx, input }) => {
            let userSettings = input.userSettings

            // Delete existing shares
            await ctx.db
                .delete(characterShares)
                .where(eq(characterShares.characterId, input.characterId))

            // Insert new shares
            if (userSettings.length > 0) {
                await ctx.db.insert(characterShares).values(
                    userSettings.map((setting) => ({
                        characterId: input.characterId,
                        sharedWithId: setting.userId,
                        customConcept: setting.customConcept
                    }))
                )
            }
        }),

    getSharedCharacters: protectedProcedure.query(async ({ ctx }) => {
        const shares = await ctx.db.query.characterShares.findMany({
            where: eq(characterShares.sharedWithId, ctx.session.user.id),
            with: {
                character: true
            }
        })
        return shares.map((s) => {
            const { name } = JSON.parse(s.character.content)
            return {
                name,
                concept: s.customConcept ?? undefined,
                imageUrl: s.character.imageUrl,
                id: s.character.id
            }
        })
    }),

    getCharacterShares: adminProcedure
        .input(
            z.object({
                characterId: z.number()
            })
        )
        .query(async ({ ctx, input }) => {
            const shares = await ctx.db.query.characterShares.findMany({
                where: eq(characterShares.characterId, input.characterId)
            })

            return shares.map((share) => ({
                userId: share.sharedWithId,
                customConcept: share.customConcept
            }))
        })
})
