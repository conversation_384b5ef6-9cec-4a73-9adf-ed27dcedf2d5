import {
    adminProcedure,
    createTRPCRouter,
} from '~/server/api/trpc';

export const userRouter = createTRPCRouter({
    getList: adminProcedure.query(async ({ ctx }) => {
        const users = await ctx.db.query.users.findMany({
            where: ctx.session.user.isAdmin
                ? undefined
                : (fields, operators) =>
                      operators.eq(fields.id, ctx.session.user.id)
        });

        return users;
    }),
});
