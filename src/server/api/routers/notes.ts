import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '~/server/api/trpc';
import { playerNotes } from '~/server/db/schema';
import { and, eq } from 'drizzle-orm';

export const notesRouter = createTRPCRouter({
    getPlayerNotes: protectedProcedure
        .input(z.object({ characterId: z.number() }))
        .query(async ({ ctx, input }) => {
            return ctx.db.query.playerNotes.findMany({
                where: eq(playerNotes.characterId, input.characterId),
                orderBy: (notes) => [notes.createdAt]
            });
        }),

    createPlayerNote: protectedProcedure
        .input(
            z.object({
                characterId: z.number(),
                title: z.string(),
                content: z.string()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const [note] = await ctx.db
                .insert(playerNotes)
                .values({
                    characterId: input.characterId,
                    title: input.title,
                    content: input.content,
                    createdById: ctx.session.user.id
                })
                .returning();
            return note;
        }),

    updatePlayerNote: protectedProcedure
        .input(
            z.object({
                id: z.number(),
                title: z.string().min(1),
                content: z.string().min(1)
            })
        )
        .mutation(async ({ ctx, input }) => {
            await ctx.db
                .update(playerNotes)
                .set({
                    title: input.title,
                    content: input.content
                })
                .where(
                    and(
                        eq(playerNotes.id, input.id),
                        eq(playerNotes.createdById, ctx.session.user.id)
                    )
                );
        }),
        
    deletePlayerNote: protectedProcedure
        .input(z.object({ id: z.number() }))
        .mutation(async ({ ctx, input }) => {
            await ctx.db
                .delete(playerNotes)
                .where(eq(playerNotes.id, input.id));
        })
});