import { z } from 'zod';
import { adminProcedure, createTR<PERSON>Router, protectedProcedure } from '~/server/api/trpc';
import { gmNotes } from '~/server/db/schema';
import { eq } from 'drizzle-orm';

export const gmNotesRouter = createTRPCRouter({
    getGmNotes: adminProcedure
        .input(z.object({ characterId: z.number() }))
        .query(async ({ ctx, input }) => {
            return ctx.db.query.gmNotes.findFirst({
                where: eq(gmNotes.characterId, input.characterId)
            });
        }),

    updateGmNotes: adminProcedure
        .input(
            z.object({
                characterId: z.number(),
                appearance: z.string().optional(),
                shortBio: z.string().optional(),
                personality: z.string().optional(),
                quirks: z.string().optional(),
                imageGenerationPrompt: z.string().optional(),
                faction: z.string().optional()
            })
        )
        .mutation(async ({ ctx, input }) => {
            const { characterId, ...updateData } = input;
            
            await ctx.db
                .update(gmNotes)
                .set({
                    ...updateData,
                    updatedById: ctx.session.user.id
                })
                .where(eq(gmNotes.characterId, characterId));
        })
});