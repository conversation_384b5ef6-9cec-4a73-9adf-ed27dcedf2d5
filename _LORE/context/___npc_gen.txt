Generate a list of 100 unique, random NPCs suitable for encounters on Theseus Station. The list must adhere strictly to the following requirements:

Format: The output must be a Markdown table with the following columns, in this exact order and in Russian:

Имя, Профессия, Место работы, Занятие (на работе), Внешность, Привычка, Занятие (в свободное время), Локация (в свободное время). Do not include a dice roll or index number column.

Language: The entire table, including all headers and content, must be in Russian. NPC names should adhere to the provided world lore -- so vast majority of them should NOT be Russian.

Professions: Professions must be mundane and appropriate for a detailed sci-fi space station setting.

Be specific: Instead of a generic "Техник", use "Техник систем жизнеобеспечения" or "Инженер по гравитационным панелям".

Avoid high-stakes roles: Do not include top-tier spies, master assassins, faction leaders, generals, or other roles that would overshadow the players. The NPCs should feel like everyday inhabitants of the station.

NPC Originality: All 100 NPCs must be original creations. Do not include any named characters mentioned in the provided lore files (e.g., Francheska, Aurora, Hassan, Jefferson, Lio, Marek, Serena, Rafa Said, San-Shi Toledo, etc.).

Naming Convention: For any NPCs with a nickname or a single-word name, provide their likely full name in parentheses. Example:  "Ржавый" (Виктор Петров).

Lore Adherence:

The list should reflect the approximate faction demographics of Theseus (~1/4 Royalist, 1/3 Corporate, 1/5 Legion, remainder independent/descendant).

The "Место работы" and "Локация (в свободное время)" columns must use the station's district names as described in the lore (e.g., "Мешки", "Ад", "Рынок", "Домен", "Кольцо", "Ось").

No Openly Carried Weapons: A critical constraint is that NPCs should not be openly carrying weapons in public sectors. Their activities, both at work and off-duty, must reflect this security norm. A guard might be checking IDs or reviewing feeds, not polishing their rifle in a public square.

Completeness: The final output must be the complete list of 300 entries. Do not truncate the list or use placeholders like "... (продолжение до 100)".

Example:
Радж Патель | Инженер по гравитационным системам | Ось, "Корпы" | Калибрует грави-панели, из-за чего пол слегка вибрирует и ощущается запах озона | Полноват и неряшлив, засохший след машинного масла на щеке | Полностью игнорирует окружающих во время работы или когда слушает музыку | Ест онигири на скамье, слушает поп-музыку и наблюдает за дронами, искусственно опыляющими картошку | Ось, "Сады"

Why it's good:
- Good name reflecting the setting, Raj is likely corporate which we can figure out by indian heritage
- Good specific profession
- Good specific location using both station sector (Ось) and level (Корпы, Сады)
- Good specific activity and how it affects people around
- Good specific visual description
- Good specific quirk/behaviour that might affect players' actions
- Great specifics in free time action: named food type, named music type, named what he watches and it subtly ties into Rori's quest to figure out what's going on with bees -- does not help or direct her but might be a reminder