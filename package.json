{"name": "charsheet", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev --turbo", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/drizzle-adapter": "^1.7.2", "@google/generative-ai": "^0.24.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.1.8", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "civitai": "^0.1.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.41.0", "lucide-react": "^0.485.0", "minio": "^8.0.5", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "postgres": "^3.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "server-only": "^0.0.1", "superjson": "^2.2.1", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "drizzle-kit": "^0.30.5", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.39.2"}, "packageManager": "npm@11.2.0"}